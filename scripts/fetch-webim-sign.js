/* eslint-disable @typescript-eslint/no-require-imports */
const { NacosConfigClient } = require('nacos');
const path = require('path');
const fs = require('fs');

const getSigmaParams = () => {
  const map = new Map();
  const LABEL_SITE = 'SIGMA_APP_SITE';
  const site = process.env[LABEL_SITE];
  const LABEL_UNIT = 'SIGMA_APP_UNIT';
  const unit = process.env[LABEL_UNIT];
  const LABEL_APP = 'SIGMA_APP_NAME';
  const app = process.env[LABEL_APP];
  const LABEL_STAGE = 'SIGMA_APP_STAGE';
  const stage = process.env[LABEL_STAGE];

  if (site) {
    map.set('site', site);
  }
  if (unit) {
    map.set('unit', unit);
  }
  if (app) {
    map.set('app', app);
  }
  if (stage) {
    map.set('stage', stage);
  }

  let stringBuilder = '';
  if (map.size > 0) {
    for (const [key, value] of map.entries()) {
      stringBuilder += `${key}:${value},`;
    }
  }

  return stringBuilder;
};

// for find address mode
const configClient = new NacosConfigClient({
  endpoint: 'jmenv.tbsite.net:8080',
  requestTimeout: 10000,
  endpointQueryParams: `nofix=1&&labels=${encodeURIComponent(getSigmaParams())}`,
  contextPath: 'diamond-server',
  clusterName: 'diamond',
});

const outputPath = path.join(__dirname, '../config/signer.json');

// get config once
configClient.getConfig(
  'com.dingtalk.protocol.request.sign',
  'dingtalk',
).then((contentString) => {
  const content = JSON.parse(contentString);

  const versionKeyList = Object.keys(content?.version || {});
  let maxVersion = parseInt(versionKeyList?.[0], 10);
  versionKeyList?.forEach((key) => {
    if (parseInt(key, 10) > maxVersion) {
      maxVersion = parseInt(key, 10);
    }
  });

  const targetConfig =
    {
      urlWhiteList: content?.urlWhiteList || [],
      version: maxVersion.toString(),
      seed: content?.version?.[maxVersion]?.seed,
      hashAlg1: content?.version?.[maxVersion]?.hashAlg1,
      hashAlg2: content?.version?.[maxVersion]?.hashAlg2,
      hashAlg3: content?.version?.[maxVersion]?.hashAlg3,
      signHeader: content?.signHeader,
      concatenationOrder: content?.version?.[maxVersion]?.concatenationOrder,
      signValidMs: content?.version?.[maxVersion]?.signValidMs,
    };

  // 修复点3：添加文件写入完成回调
  fs.writeFile(
    outputPath,
    JSON.stringify(targetConfig, null, 2),
    (err) => {
      if (err) {
        console.error('文件写入失败:', err);
        process.exit(1);
      }
      console.log('配置已写入 signer.json');
      process.exit(0); // 明确退出进程
    },
  );
});
