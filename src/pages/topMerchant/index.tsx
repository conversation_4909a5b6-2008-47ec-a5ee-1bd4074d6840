import { i18next } from '@ali/dingtalk-i18n';
import { useEffect, useState } from 'react';
import { sendUT } from '@/utils/trace';
import { openLink, openDualLink, setPageTitle } from '@/utils/jsapi';
import OptimizedImage from '@/components/OptimizedImage';
import Loading from '@/components/Loading';
import { SearchOutlined } from '@ali/ding-icons';
import { queryGoodShoppExpertDetail } from '@/apis/production';
import { Toast, Button } from 'dingtalk-design-mobile';
import { getUrlParam, getDecodedUrlParam, formatNumber } from '@/utils/util';
import { getLwpSourceUrl } from '@/utils/env';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';

import './index.less';

const TopMerchant = () => {
  const params = getUrlParam() as Record<string, string>;
  const { platform, agentCode, userId } = params;
  const bizId = getDecodedUrlParam('bizId');
  const [activeStyleTab, setActiveStyleTab] = useState<'highest' | 'recent'>('highest');
  const [activeMerchantTab, setActiveMerchantTab] = useState<'sales' | 'recent'>('sales');
  const [loading, setLoading] = useState<boolean>(true);
  const [merchantInfo, setMerchantInfo] = useState<any>({});
  const [likeItems, setLikeItems] = useState<any[]>([]);
  const [publishTimeItems, setPublishTimeItems] = useState<any[]>([]);
  const [kolSalesCountItems, setKolSalesCountItems] = useState<any[]>([]);
  const [kolPublishTimeItems, setKolPublishTimeItems] = useState<any[]>([]);

  // Pagination states
  const [salesDisplayCount, setSalesDisplayCount] = useState<number>(20);
  const [recentDisplayCount, setRecentDisplayCount] = useState<number>(20);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);

  // Style notes pagination states
  const [likeItemsDisplayCount, setLikeItemsDisplayCount] = useState<number>(10);
  const [publishTimeItemsDisplayCount, setPublishTimeItemsDisplayCount] = useState<number>(10);
  const [isLoadingMoreStyle, setIsLoadingMoreStyle] = useState<boolean>(false);

  useEffect(() => {
    setPageTitle(i18next.t('j-dingtalk-web_pages_topMerchant_GoodNumberDetails'));

    sendUT('top_merchant_page_view', {});

    initData();
  }, []);

  // Scroll event listener for lazy loading
  useEffect(() => {
    const handleScroll = () => {
      if (isLoadingMore) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // Load more when user scrolls to 200px from bottom
      if (scrollTop + windowHeight >= documentHeight - 200) {
        loadMoreProducts();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [
    activeMerchantTab,
    salesDisplayCount,
    recentDisplayCount,
    kolSalesCountItems,
    kolPublishTimeItems,
    isLoadingMore]);

  const initData = async () => {
    try {
      setLoading(true);
      const res = await queryGoodShoppExpertDetail({
        platform,
        agentCode,
        bizId,
        code: userId,
      });
      const { redKolDetailResponse, dyKolDetailResponse } = res || {};
      if (redKolDetailResponse) {
        setMerchantInfo(redKolDetailResponse || {});
        setKolSalesCountItems(redKolDetailResponse?.redKolItemListMap?.sales_count || []);
        setKolPublishTimeItems(redKolDetailResponse?.redKolItemListMap?.publish_time);
        setLikeItems(redKolDetailResponse?.redKolNoteMap?.like_cnt || []);
        setPublishTimeItems(redKolDetailResponse?.redKolNoteMap?.publish_time || []);
      } else if (dyKolDetailResponse) {
        setMerchantInfo(dyKolDetailResponse || {});
        setKolSalesCountItems(dyKolDetailResponse?.dyKolItemListMap?.sales_count || []);
        setKolPublishTimeItems(dyKolDetailResponse?.dyKolItemListMap?.publish_time || []);
        setLikeItems(dyKolDetailResponse?.dyKolNoteMap?.like_cnt || []);
        setPublishTimeItems(dyKolDetailResponse?.dyKolNoteMap?.publish_time || []);
      }
    } catch (err) {
      Toast.fail({
        content: err?.reason || i18next.t('j-dingtalk-web_pages_topMerchant_FailedToObtainData'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleProductClick = (dspcCode: string) => {
    sendUT('merchant_product_click', { productId: dspcCode });
    if (!dspcCode) {
      return;
    }
    const sourceUrl = getLwpSourceUrl(dspcCode);
    openLink(sourceUrl);
  };

  const handleStyleTabChange = (tab: 'highest' | 'recent') => {
    setActiveStyleTab(tab);
    sendUT('style_tab_change', { tab });

    // Reset display count when switching tabs
    if (tab === 'highest') {
      setLikeItemsDisplayCount(10);
    } else {
      setPublishTimeItemsDisplayCount(10);
    }
  };

  const handleMerchantTabChange = (tab: 'sales' | 'recent') => {
    setActiveMerchantTab(tab);
    sendUT('merchant_tab_change', { tab });

    // Reset display count when switching tabs
    if (tab === 'sales') {
      setSalesDisplayCount(20);
    } else {
      setRecentDisplayCount(20);
    }
  };

  // 笔记详情
  const handleNoteClick = (note: any) => {
    sendUT('style_note_click', { noteId: note.noteId });
    const videoLink = JSON.parse(note?.videoLink || '[]')?.[0];

    if (!videoLink) {
      return;
    }

    $openLink({
      url: openDualLink(videoLink),
    });
  };

  // 类似商品寻源
  const handleSimilarProductClick = (dspcCode: string) => {
    sendUT('similar_product_click', { dspcCode });
    if (!dspcCode) {
      return;
    }

    const sourceUrl = getLwpSourceUrl(dspcCode);
    openLink(sourceUrl);
  };

  // Load more products function
  const loadMoreProducts = () => {
    if (isLoadingMore) return;

    // Check if all data is already loaded
    if (activeMerchantTab === 'sales') {
      const currentCount = salesDisplayCount;
      const totalItems = kolSalesCountItems.length;
      if (currentCount >= totalItems) {
        return; // All data is already loaded, no need to load more
      }
    } else {
      const currentCount = recentDisplayCount;
      const totalItems = kolPublishTimeItems.length;
      if (currentCount >= totalItems) {
        return; // All data is already loaded, no need to load more
      }
    }

    setIsLoadingMore(true);

    // Simulate loading delay
    setTimeout(() => {
      if (activeMerchantTab === 'sales') {
        const currentCount = salesDisplayCount;
        const totalItems = kolSalesCountItems.length;
        if (currentCount < totalItems) {
          setSalesDisplayCount(Math.min(currentCount + 20, totalItems));
        }
      } else {
        const currentCount = recentDisplayCount;
        const totalItems = kolPublishTimeItems.length;
        if (currentCount < totalItems) {
          setRecentDisplayCount(Math.min(currentCount + 20, totalItems));
        }
      }
      setIsLoadingMore(false);
    }, 500);
  };

  // Load more style notes function
  const loadMoreStyleNotes = () => {
    if (isLoadingMoreStyle) return;

    setIsLoadingMoreStyle(true);

    // Simulate loading delay
    setTimeout(() => {
      if (activeStyleTab === 'highest') {
        const currentCount = likeItemsDisplayCount;
        const totalItems = likeItems.length;
        if (currentCount < totalItems) {
          setLikeItemsDisplayCount(Math.min(currentCount + 10, totalItems));
        }
      } else {
        const currentCount = publishTimeItemsDisplayCount;
        const totalItems = publishTimeItems.length;
        if (currentCount < totalItems) {
          setPublishTimeItemsDisplayCount(Math.min(currentCount + 10, totalItems));
        }
      }
      setIsLoadingMoreStyle(false);
    }, 500);
  };

  // Show loading while data is being fetched
  if (loading) {
    return <Loading />;
  }

  return (
    <div className="top-merchant">
      {/* Header Section */}
      <div className="merchant-header">
        <div className="merchant-info">
          {
            merchantInfo?.avatar ?
              <div className="merchant-avatar">
                <OptimizedImage
                  src={merchantInfo?.avatar}
                  alt={merchantInfo?.userName}
                  width={52}
                  height={52}
                  lazy
                  progressive
                  quality={90}
                  className="merchant-avatar-image"
                />

              </div> :
              null
          }
          <div className="merchant-details">
            <div className="merchant-title">
              <div className="merchant-name">{merchantInfo?.userName}</div>
              {
                merchantInfo?.category &&
                <div className="merchant-tag">{merchantInfo?.category}</div>

              }
            </div>
            <div className="merchant-stats">
              {merchantInfo?.fanNumDesc !== undefined &&
              <span className="fans-count">
                <span className="fans-num">{merchantInfo?.fanNumDesc}</span>
                {i18next.t('j-dingtalk-web_pages_topMerchant_Fans')}
              </span>
              }
              {merchantInfo?.likeNumDesc !== undefined &&
              <span className="fans-count">
                <span className="fans-num">{merchantInfo?.likeNumDesc}</span>
                {i18next.t('j-dingtalk-web_pages_topMerchant_NumberOfInteractions')}
              </span>
              }
            </div>
          </div>
        </div>
      </div>
      {/* Data Overview */}
      <div className="data-overview">
        <h3 className="section-title">
          {i18next.t('j-dingtalk-web_pages_topMerchant_DataOverview')}
        </h3>
        <div className="data-grid">
          {merchantInfo?.followNumDesc !== undefined &&
          <div className="data-item">
            <div className="data-label">
              {i18next.t('j-dingtalk-web_pages_topMerchant_NumberOfConcerns')}
            </div>
            <div className="data-value">{merchantInfo?.followNumDesc}</div>
          </div>
          }
          {merchantInfo?.collectNumDesc !== undefined &&
          <div className="data-item">
            <div className="data-label">
              {i18next.t('j-dingtalk-web_pages_topMerchant_Favorites')}
            </div>
            <div className="data-value">{merchantInfo?.collectNumDesc}</div>
          </div>
          }
          {merchantInfo?.goodsNumDesc !== undefined &&
          <div className="data-item">
            <div className="data-label">{i18next.t('j-dingtalk-web_pages_topMerchant_NumberOfProducts')}

            </div>
            <div className="data-value">
              {i18next.t('j-dingtalk-web_pages_topMerchant_Mockmerchantinfobrandcount', {
                merchantInfoGoodsNum: merchantInfo?.goodsNumDesc,
              })}
            </div>
          </div>
          }
        </div>
      </div>
      {/* Style Overview */}
      {
        likeItems?.length > 0 && publishTimeItems?.length > 0 &&
        <div className="style-overview">
          <h3 className="section-title">
            {i18next.t('j-dingtalk-web_pages_topMerchant_StyleOverview')}
          </h3>
          <div className="interaction-header">
            <span
              className={`interaction-label ${activeStyleTab === 'highest' ? 'active' : ''}`}
              onClick={() => handleStyleTabChange('highest')}
            >
              {i18next.t('j-dingtalk-web_pages_topMerchant_HighestInteraction')}
            </span>
            <span
              className={`interaction-label ${activeStyleTab === 'recent' ? 'active' : ''}`}
              onClick={() => handleStyleTabChange('recent')}
            >


              {i18next.t('j-dingtalk-web_pages_topMerchant_RecentlyReleased')}
            </span>
          </div>
          {/* Style Notes List */}
          {
            likeItems?.length > 0 &&
            <div className={`style-notes-list ${activeStyleTab === 'highest' ? 'active' : ''}`}>
              {likeItems?.slice(0, likeItemsDisplayCount).map((note) =>

                (
                  <div key={note.nodeId} className="style-note-item">
                    <div className="style-note-image-container" onClick={() => handleNoteClick(note)}>
                      <OptimizedImage
                        src={note?.ossCover}
                        alt={note?.titleJp || note?.title}
                        width={164}
                        height={212}
                        lazy
                        progressive
                        quality={90}
                        className="style-note-image"
                      />

                      <img
                        src="https://img.alicdn.com/imgextra/i4/O1CN01tTC9ES1jUPZ16orvo_!!6000000004551-2-tps-137-143.png"
                        alt="play"
                        className="play-icon"
                        width={32}
                        height={32}
                      />

                    </div>
                    <div className="style-note-info">
                      <p className="style-note-description">{note?.titleJp || note?.title}</p>
                      <div className="style-note-likes">
                        <img
                          src="https://img.alicdn.com/imgextra/i1/O1CN01yM4Y8e29Vwu4ExXd0_!!6000000008074-2-tps-64-64.png"
                          className="like-icon"
                          alt="like"
                          width={16}
                          height={16}
                        />


                        <span className="likes-count">{note?.likeCnt}</span>
                      </div>
                      <div className="style-note-desc">
                        {platform === 'dy_kol' ? i18next.t('j-dingtalk-web_pages_topMerchant_ProductsMentionedInThisVideo') : i18next.t('j-dingtalk-web_pages_topMerchant_TheGoodsMentionedInThis')}
                      </div>
                      <div className="style-note-products">
                        {note?.productList?.map((product) =>


                          (
                            <div
                              key={product.id}
                              className="style-note-product"
                              onClick={() => handleSimilarProductClick(product?.dspcCode)}
                            >


                              <OptimizedImage
                                src={product?.imageUrl}
                                alt={product?.titleJp || product?.title}
                                height={98}
                                lazy
                                progressive
                                quality={90}
                                className="style-note-product-image"
                              />


                              <div className="product-search-overlay">
                                <SearchOutlined className="search-icon" />
                                <span className="search-text">
                                  {i18next.t('j-dingtalk-web_pages_topMerchant_SimilarProducts')}
                                </span>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </div>
                ))}

              {/* Load more button for highest interaction */}
              {activeStyleTab === 'highest' && likeItemsDisplayCount < likeItems.length &&
              <div className="load-more-button-container">
                <Button
                  type="primary"
                  size="small"
                  loading={isLoadingMoreStyle}
                  onClick={loadMoreStyleNotes}
                  className="load-more-button"
                >
                  {isLoadingMoreStyle ?
                    i18next.t('j-dingtalk-web_components_Loading_Loading') :
                    i18next.t('j-dingtalk-web_pages_topMerchant_LoadMore')
                  }
                </Button>
              </div>
              }
            </div>
          }
          {/* Style Notes List */}
          {
            publishTimeItems?.length > 0 &&
            <div className={`style-notes-list ${activeStyleTab === 'recent' ? 'active' : ''}`}>
              {publishTimeItems?.slice(0, publishTimeItemsDisplayCount).map((note) =>

                (
                  <div key={note.id} className="style-note-item">
                    <div className="style-note-image-container" onClick={() => handleNoteClick(note)}>
                      <OptimizedImage
                        src={note?.ossCover}
                        alt={note?.titleJp || note?.title}
                        width={164}
                        height={212}
                        lazy
                        progressive
                        quality={90}
                        className="style-note-image"
                      />

                      <img
                        src="https://img.alicdn.com/imgextra/i4/O1CN01tTC9ES1jUPZ16orvo_!!6000000004551-2-tps-137-143.png"
                        alt="play"
                        className="play-icon"
                        width={32}
                        height={32}
                      />


                    </div>
                    <div className="style-note-info">
                      <p className="style-note-description">{note?.titleJp || note?.title}</p>
                      <div className="style-note-likes">
                        <img
                          src="https://img.alicdn.com/imgextra/i1/O1CN01yM4Y8e29Vwu4ExXd0_!!6000000008074-2-tps-64-64.png"
                          className="like-icon"
                          alt="like"
                          width={16}
                          height={16}
                        />


                        <span className="likes-count">{note?.likeCnt}</span>
                      </div>
                      <div className="style-note-desc">
                        {i18next.t('j-dingtalk-web_pages_topMerchant_TheGoodsMentionedInThis')}
                      </div>
                      <div className="style-note-products">
                        {note?.productList?.map((product) =>

                          (
                            <div
                              key={product.id}
                              className="style-note-product"
                              onClick={() => handleSimilarProductClick(product?.dspcCode)}
                            >

                              <OptimizedImage
                                src={product?.imageUrl}
                                alt={product?.titleJp || product?.title}
                                height={98}
                                lazy
                                progressive
                                quality={90}
                                className="style-note-product-image"
                              />

                              <div className="product-search-overlay">
                                <SearchOutlined className="search-icon" />
                                <span className="search-text">
                                  {i18next.t('j-dingtalk-web_pages_topMerchant_SimilarProducts')}
                                </span>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </div>
                ))}
              {/* Load more button for recent */}
              {activeStyleTab === 'recent' && publishTimeItemsDisplayCount < publishTimeItems.length &&
              <div className="load-more-button-container">
                <Button
                  type="primary"
                  size="small"
                  loading={isLoadingMoreStyle}
                  onClick={loadMoreStyleNotes}
                  className="load-more-button"
                >

                  {isLoadingMoreStyle ?
                    i18next.t('j-dingtalk-web_components_Loading_Loading') :
                    i18next.t('j-dingtalk-web_pages_topMerchant_LoadMore')
                  }
                </Button>
              </div>
              }
            </div>
          }
        </div>
      }
      {/* Merchant Products */}
      {
        kolSalesCountItems?.length > 0 && kolPublishTimeItems?.length > 0 &&
        <div className="merchant-products">
          <h3 className="section-title">
            {i18next.t('j-dingtalk-web_pages_topMerchant_ProductsSoldByTa')}
          </h3>
          <div className="interaction-header">
            <span
              className={`interaction-label ${activeMerchantTab === 'sales' ? 'active' : ''}`}
              onClick={() => handleMerchantTabChange('sales')}
            >

              {i18next.t('j-dingtalk-web_pages_topMerchant_HighestSalesVolume')}
            </span>
            <span
              className={`interaction-label ${activeMerchantTab === 'recent' ? 'active' : ''}`}
              onClick={() => handleMerchantTabChange('recent')}
            >

              {i18next.t('j-dingtalk-web_pages_topMerchant_RecentlyReleased')}
            </span>
          </div>
          {
            activeMerchantTab === 'sales' && kolSalesCountItems?.length > 0 &&
            <div className="product-grid">
              {kolSalesCountItems?.slice(0, salesDisplayCount).map((product) =>


                (
                  <div
                    key={product.itemId}
                    className="product-item"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProductClick(product.dspcCode);
                    }}
                  >


                    <div className="product-image-container">
                      <OptimizedImage
                        src={product.itemImg}
                        alt={product?.itemTitleJp || product?.itemTitle}
                        className="product-image"
                        width="100%"
                        height="auto"
                        lazy
                        progressive
                        quality={90}
                      />


                      <div className="product-actions">
                        <button
                          className="similar-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleProductClick(product.dspcCode);
                          }}
                        >


                          <SearchOutlined className="similar-icon" />
                          {i18next.t('j-dingtalk-web_pages_topMerchant_SimilarProducts_1')}
                        </button>
                      </div>
                    </div>
                    <div className="product-info">
                      <h4 className="product-title">{product?.itemTitleJp || product?.itemTitle}</h4>
                      <div className="product-price">
                        {product.salePrice}
                        <span>円</span>
                      </div>
                      {
                        product.sales !== undefined && <div className="product-sales">{`${i18next.t('j-dingtalk-web_pages_topMerchant_Sold')}${formatNumber(product.sales)}${i18next.t('j-dingtalk-web_pages_topMerchant_Pieces')}`}</div>
                      }
                    </div>
                  </div>
                ))}
            </div>
          }
          {
            activeMerchantTab === 'recent' && kolPublishTimeItems?.length > 0 &&
            <div className="product-grid">
              {kolPublishTimeItems?.slice(0, recentDisplayCount).map((product) =>

                (
                  <div
                    key={product.itemId}
                    className="product-item"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProductClick(product.dspcCode);
                    }}
                  >


                    <div className="product-image-container">
                      <OptimizedImage
                        src={product.itemImg}
                        alt={product?.itemTitleJp || product?.itemTitle}
                        className="product-image"
                        width="100%"
                        height="auto"
                        lazy
                        progressive
                        quality={90}
                      />


                      <div className="product-actions">
                        <button
                          className="similar-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleProductClick(product.dspcCode);
                          }}
                        >


                          <SearchOutlined className="similar-icon" />
                          {i18next.t('j-dingtalk-web_pages_topMerchant_SimilarProducts_1')}
                        </button>
                      </div>
                    </div>
                    <div className="product-info">
                      <h4 className="product-title">{product?.itemTitleJp || product?.itemTitle}</h4>
                      <div className="product-price">
                        {product.salePrice}
                        <span>円</span>
                      </div>
                      {
                        product.sales !== undefined && <div className="product-sales">{`${i18next.t('j-dingtalk-web_pages_topMerchant_Sold')}${formatNumber(product.sales)}${i18next.t('j-dingtalk-web_pages_topMerchant_Pieces')}`}</div>
                      }
                    </div>
                  </div>
                ))}
            </div>
          }

          {/* Loading more indicator */}
          {isLoadingMore &&
          <div className="loading-more-container">
            <div className="loading-more-text">
              {i18next.t('j-dingtalk-web_components_Loading_Loading')}...
            </div>
          </div>
          }

          {/* Show "No more data" when all items are loaded */}
          {!isLoadingMore && (
            (activeMerchantTab === 'sales' &&
        salesDisplayCount >= kolSalesCountItems.length &&
        kolSalesCountItems.length > 20) ||
        (activeMerchantTab === 'recent' && recentDisplayCount >= kolPublishTimeItems.length && kolPublishTimeItems.length > 20)) &&

        <div className="no-more-data-container">
          <div className="no-more-data-text">
            {i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_NoMore')}
          </div>
        </div>
          }
        </div>

      }
    </div>);
};

export default TopMerchant;
