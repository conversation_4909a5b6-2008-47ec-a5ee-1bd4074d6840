// AI Image page type definitions

export interface ImageInfo {
  uuid: string;
  imgUrls: string[]; // Array containing 4 images from targetImgUrl
  originImgUrl?: string; // sourceImgUrl from API
  type: 'normal' | 'hd'; // 普通图片normal,高清图片：hd
  userRating?: string;
  status: string; // pending, processing, finish, failed
  errorMsg?: string;
  createdAt: string; // API field: createdAt (timestamp)
  finishTime?: string; // API field: finishTime (timestamp)
  requestId?: string; // API field: requestId
  // Additional API fields
  requirements?: string; // API field: requirements (prompt description)
  imgPixel?: string; // API field: imgPixel (image size)
  operator?: string; // API field: operator
  updatedAt?: string; // API field: updatedAt
  // Frontend extension fields
  prompt?: string;
  imageSize?: string;
  progress?: number;
  liked?: boolean;
  disliked?: boolean;
}

// Main state interface for AI Image generation
export interface AIImageState {
  currentStep: 'welcome' | 'form' | 'imageList';
  rightPanelContent: 'welcome' | 'imageList'; // For PC layout right panel
  // Form state
  uploadedImage: string | null;
  positivePrompt: string;
  imageSize: string;
  isGenerating: boolean;
  generatedImageUrl: string | null;
  error: string | null;
  taskId: string | null;
  progress: number;
  // Image list state
  imageList: ImageInfo[];
  isLoadingImages: boolean;
  hasImages: boolean;
  imageListError: string | null;
  nextUuid: string | null;
  hasNextPage: boolean;
  progressMap: Record<string, number>; // Map of image UUID to progress percentage
  isLoadingMore: boolean; // Loading state for pagination
}

// Image form state interface
export interface ImageFormState {
  uploadedImage: string | null;
  positivePrompt: string;
  imageSize: string;
  isGenerating: boolean;
  error: string | null;
}
