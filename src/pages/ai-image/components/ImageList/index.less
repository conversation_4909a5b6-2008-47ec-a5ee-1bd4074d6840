.image-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #000000;

  // Empty state styles
  .image-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    padding: 40px 20px;
    text-align: center;

    .empty-icon {
      font-size: 64px;
      margin-bottom: 24px;
      opacity: 0.6;
    }

    .empty-title {
      font-size: 20px;
      font-weight: 600;
      color: rgba(255,255,255,0.8);
      margin-bottom: 8px;
    }

    .empty-description {
      font-size: 14px;
      color: rgba(255,255,255,0.4);
      margin-bottom: 32px;
      line-height: 20px;
    }

    .create-first-image-btn {
      min-width: 140px;
      height: 44px;
      border-radius: 22px;
      font-size: 16px;
      font-weight: 500;

      .anticon {
        margin-right: 8px;
      }
    }
  }

  // Error state styles
  .image-list-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    padding: 40px 20px;
    text-align: center;

    .error-icon {
      font-size: 64px;
      margin-bottom: 24px;
      opacity: 0.6;
    }

    .error-title {
      font-size: 20px;
      font-weight: 600;
      color: rgba(255,255,255,0.8);
      margin-bottom: 8px;
    }

    .error-description {
      font-size: 14px;
      color: rgba(255,255,255,0.4);
      margin-bottom: 32px;
      line-height: 20px;
    }

    .error-actions {
      display: flex;
      gap: 12px;
      align-items: center;

      .retry-btn,
      .create-new-btn {
        min-width: 120px;
        height: 40px;
        border-radius: 20px;
        font-size: 14px;

        .anticon {
          margin-right: 6px;
        }
      }
    }
  }

  // Loading state styles
  .image-list-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    padding: 40px 20px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255,255,255,0.16);
      border-top: 3px solid rgba(48,128,219,1);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 24px;
      // Performance optimizations for low-end devices
      will-change: transform;
      transform: translateZ(0); // Force hardware acceleration
    }

    .loading-text {
      font-size: 14px;
      color: rgba(255,255,255,0.4);
    }
  }

  // Image list content styles
  .image-list-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;

    // Mobile responsive adjustments
    @media (max-width: 768px) {
      height: auto;
      min-height: 100vh;
    }

    .image-list-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid rgba(255,255,255,0.16);
      background-color: rgba(17,18,19,1);
      position: sticky;
      top: 0;
      z-index: 10;

      .header-title {
        display: flex;
        align-items: center;
        gap: 16px;

        .logo {
          width: 24px;
          height: 24px;
        }

        .image-title {
          font-size: 16px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .create-image-btn {
        font-size: 24px;
        color: rgba(255, 255, 255, 0.9);
        cursor: pointer;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }

        &:active {
          color: white;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }

    .image-list-items {
      flex: 1;
      padding: 0 16px 16px;
      overflow-y: auto;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
      
      // Enhanced performance optimizations for iOS
      -webkit-overflow-scrolling: touch; // Smooth scrolling on iOS
      contain: layout style paint; // CSS containment for better performance
      will-change: scroll-position; // Optimize for scrolling
      transform: translate3d(0, 0, 0); // Force hardware acceleration

      // Reduce repaints during scrolling
      -webkit-transform: translate3d(0, 0, 0);
      -moz-transform: translate3d(0, 0, 0);
      -ms-transform: translate3d(0, 0, 0);

      // Prevent unnecessary reflows
      overflow-anchor: none;

      // Mobile responsive adjustments
      @media (max-width: 768px) {
        overflow-y: visible;
        flex: none;
        grid-template-columns: 1fr;
        gap: 16px;
        // Remove hardware acceleration on mobile to save memory
        will-change: auto;
        transform: none;
        -webkit-transform: none;
      }

      .image-list-item {
        margin-bottom: 0;
        // Enhanced containment for better rendering performance
        contain: layout style paint;
        // Optimize individual items
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        // Reduce layout thrashing
        isolation: isolate;
      }
    }

    .load-more-section {
      padding: 20px;
      text-align: center;
      border-top: 1px solid rgba(255,255,255,0.16);

      .load-more-text {
        font-size: 14px;
        color: rgba(255,255,255,0.4);
      }

      .load-more-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .loading-spinner {
          width: 24px;
          height: 24px;
          border: 2px solid rgba(255,255,255,0.16);
          border-top: 2px solid rgba(48,128,219,1);
          border-radius: 50%;
          animation: spin 1s linear infinite;
          // Performance optimizations for low-end devices
          will-change: transform;
          transform: translateZ(0); // Force hardware acceleration
        }

        .loading-text {
          font-size: 14px;
          color: rgba(255,255,255,0.4);
        }
      }
    }
  }

}

// Keyframes for loading spinner
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
