import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { i18next } from '@ali/dingtalk-i18n';
import { Toast } from 'dingtalk-design-mobile';
import {
  CloseOutlined,
  DownloadAndSaveLOutlined,
  VideoPlayOutlined,
  // HDModeOutlined,
} from '@ali/ding-icons';
import { isMobileDevice } from '@/utils/jsapi';
import { downloadImage } from '@/utils/download';
import { sendUT } from '@/utils/trace';
import './index.less';

interface ImagePreviewProps {
  visible: boolean;
  imageUrl: string;
  onClose: () => void;
  onNavigateToVideo?: (imageUrl: string) => void;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  visible,
  imageUrl,
  onClose,
  onNavigateToVideo,
}) => {
  const isMobile = isMobileDevice();
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  // Handle animation state and body scroll lock
  useEffect(() => {
    if (visible) {
      // Show modal and start enter animation
      setShouldRender(true);
      // Store current scroll position
      const { scrollY } = window;

      // Prevent body scroll and maintain scroll position
      document.body.classList.add('image-preview-open');
      document.body.style.top = `-${scrollY}px`;

      // Trigger enter animation after render
      requestAnimationFrame(() => {
        setIsAnimating(true);
      });

      return () => {
        // Restore body scroll and scroll position
        document.body.classList.remove('image-preview-open');
        document.body.style.top = '';
        window.scrollTo(0, scrollY);
      };
    } else if (shouldRender) {
      // Start exit animation
      setIsAnimating(false);
      // Hide modal after animation completes
      const timer = setTimeout(() => {
        setShouldRender(false);
      }, 200); // Match animation duration

      return () => clearTimeout(timer);
    }
  }, [visible, shouldRender]);

  // Handle download image
  const handleDownload = async () => {
    sendUT('aigc_picture_download', {
      device: isMobile ? 'mobile' : 'pc',
      imageUrl,
    });

    try {
      await downloadImage(imageUrl, Date.now().toString());
      Toast.success({
        content: i18next.t('j-dingtalk-web_pages_ai-image_components_ImagePreview_DownloadSuccess'),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
    } catch (error) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_ai-image_components_ImagePreview_DownloadFailed'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }
  };

  // Handle AI video generation navigation
  const handleAIVideo = () => {
    if (onNavigateToVideo) {
      onNavigateToVideo(imageUrl);
    }
  };

  // // Handle HD enhancement (coming soon)
  // const handleHDEnhancement = () => {
  //   sendUT('aigc_picture_hd_enhancement', {
  //     device: isMobile ? 'mobile' : 'pc',
  //   });
  // };

  // Handle backdrop click to close
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Prevent event propagation when clicking on content
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  if (!shouldRender) {
    return null;
  }

  const modalContent = (
    <div
      className={`image-preview-overlay ${isAnimating ? 'animate-in' : 'animate-out'}`}
      onClick={handleBackdropClick}
    >
      {/* Close button */}
      <button className="close-button" onClick={onClose}>
        <CloseOutlined />
      </button>

      {/* Image container */}
      <div className="image-preview-content" onClick={handleContentClick}>
        <div className="image-container">
          <img src={imageUrl} alt="Preview image" className="preview-image" />
        </div>

        {/* Action buttons */}
        <div className={`action-buttons ${isMobile ? 'mobile' : 'desktop'}`}>
          <button className="action-btn download-btn" onClick={handleDownload}>
            <DownloadAndSaveLOutlined />
            <span>{i18next.t('j-dingtalk-web_pages_ai-image_components_ImagePreview_Download')}</span>
          </button>

          <button className="action-btn video-btn" onClick={handleAIVideo}>
            <VideoPlayOutlined />
            <span>{i18next.t('j-dingtalk-web_pages_ai-image_components_ImagePreview_AIVideoGeneration')}</span>
          </button>

          {/* <button className="action-btn enhance-btn" onClick={handleHDEnhancement}>
            <HDModeOutlined />
            <span>{i18next.t('j-dingtalk-web_pages_ai-image_components_ImagePreview_HDUpscale')}</span>
          </button> */}
        </div>
      </div>
    </div>
  );

  // Use portal to render modal at document body level
  return createPortal(modalContent, document.body);
};

export default ImagePreview;
