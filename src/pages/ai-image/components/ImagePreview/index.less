.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  z-index: 888;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  padding-bottom: calc(20px + env(safe-area-inset-bottom));
  overflow: hidden;
  transition: opacity 0.2s ease-out, backdrop-filter 0.2s ease-out,
    -webkit-backdrop-filter 0.2s ease-out;

  &.animate-in {
    opacity: 1;
    backdrop-filter: blur(20px);
  }

  &.animate-out {
    opacity: 0;
    backdrop-filter: blur(0px);

    .image-preview-content {
      transform: scale(0.9);
      opacity: 0;
    }
  }

  &.animate-in {
    .image-preview-content {
      transform: scale(1);
      opacity: 1;
    }

    .close-button {
      opacity: 1;
      transform: translateZ(0) scale(1);
    }
  }

  .close-button {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(20px);
    color: #ffffff;
    font-size: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    transform: translateZ(0);
    opacity: 0;
    transform: translateZ(0) scale(0.8);
    transition: opacity 0.2s ease-out 0.1s, transform 0.2s ease-out 0.1s, background 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.7);
      transform: translateZ(0) scale(1.05);
    }

    &:active {
      transform: translateZ(0) scale(0.95);
    }
  }

  .image-preview-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;

    transform: scale(0.9);
    opacity: 0;
    transition: transform 0.2s ease-out, opacity 0.2s ease-out;

    .image-container {
      flex: 1;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      // Reserve space for action buttons and safe areas
      max-height: calc(100vh - 160px);
      overflow: hidden;

      .preview-image {
        max-width: calc(100vw - 40px);
        max-height: calc(100vh - 160px);
        width: auto;
        height: auto;
        object-fit: contain;
        border-radius: 8px;
      }
    }

    .action-buttons {
      position: fixed;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 16px;
      padding: 16px;

      &.mobile {
        flex-direction: column;
        gap: 12px;
        padding: 12px;
        width: calc(100% - 40px);
        max-width: 300px;
      }

      &.desktop {
        flex-direction: row;
        padding: 10px 16px;
        bottom: 0;
      }

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 20px;
        border: none;
        border-radius: 40px;
        background: rgba(0, 0, 0, 0.5);
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        cursor: pointer;
        backdrop-filter: blur(20px);
        min-width: 140px;
        transition: transform 0.2s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.6);
          transform: translateY(-2px);
        }

        &:active {
          background: rgba(0, 0, 0, 0.7);
          transform: translateY(0);
        }

        svg {
          font-size: 24px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 10px 0;

    .close-button {
      width: 40px;
      height: 40px;
      font-size: 18px;

      position: fixed;
      transform: translateZ(0) scale(0.8);

      &:hover {
        transform: translateZ(0) scale(1.05);
      }

      &:active {
        transform: translateZ(0) scale(0.95);
      }
    }

    .image-preview-content {
      .image-container {
        margin-bottom: 80px;
        // Adjust max height for mobile to reserve more space for buttons
        max-height: calc(100vh - 220px);

        .preview-image {
          max-width: calc(100vw - 20px);
          max-height: calc(100vh - 220px);
        }
      }

      .action-buttons.mobile {
        width: calc(100% - 20px);

        .action-btn {
          font-size: 16px;
          min-width: 130px;
        }
      }
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .image-preview-content {
      .image-container {
        // Adjust max height for tablet to reserve space for buttons
        max-height: calc(100vh - 180px);

        .preview-image {
          max-width: calc(100vw - 40px);
          max-height: calc(100vh - 180px);
        }
      }

      .action-buttons.desktop {
        padding: 14px 20px;

        .action-btn {
          padding: 10px 16px;
          font-size: 13px;
          min-width: 140px;
        }
      }
    }
  }
}

body.image-preview-open {
  overflow: hidden;
}
