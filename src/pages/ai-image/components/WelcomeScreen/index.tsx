import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { Button } from 'dingtalk-design-mobile';
import { PictureOutlined } from '@ali/ding-icons';
import { QuotaUsageResponse } from '@/apis/quota';
import FeaturePromotion from '@/components/FeaturePromotion';
import PcHeaderActions from '@/components/PcHeaderActions';
import { isMobileDevice } from '@/utils/jsapi';
import './index.less';

interface WelcomeScreenProps {
  quotaUsage: QuotaUsageResponse;
  hasGenerateBtn: boolean;
  onGetStarted?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onOrderClick?: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  quotaUsage,
  hasGenerateBtn,
  onGetStarted = () => {},
  onOrderClick,
}) => {
  const isMobile = isMobileDevice();

  return (
    <div className="ai-image-welcome-screen">
      {/* PC 端右上角功能区域 */}
      {!isMobile && onOrderClick && (
        <PcHeaderActions
          quotaUsage={quotaUsage}
          onOrderClick={onOrderClick}
          shareConfig={{
            title: i18next.t('j-dingtalk-web_pages_ai-image_components_WelcomeScreen_AiPortraitGeneration'),
            content: i18next.t('j-dingtalk-web_pages_ai-image_components_WelcomeScreen_TheBackgroundReplacementAndAutomatic'),
            image: 'https://img.alicdn.com/imgextra/i3/O1CN01a9s5S31GHTXGRhRgq_!!6000000000597-2-tps-192-192.png',
          }}
        />
      )}

      <div className="welcome-content">
        <div className="logo-section">
          <PictureOutlined className="logo-image" />
          <h1 className="title">{i18next.t('j-dingtalk-web_pages_ai-image_components_WelcomeScreen_AiPortraitGeneration')}</h1>
          <p className="subtitle">{i18next.t('j-dingtalk-web_pages_ai-image_components_WelcomeScreen_TheBackgroundReplacementAndAutomatic')}
            <br />{i18next.t('j-dingtalk-web_pages_ai-image_components_WelcomeScreen_GeneratePersonalizedHdImages')}
          </p>
        </div>

        {hasGenerateBtn &&
        <Button
          type="primary"
          size="large"
          className="get-started-btn"
          onClick={onGetStarted}
        >
          {i18next.t('j-dingtalk-web_pages_ai-image_components_WelcomeScreen_StartCreating')}
        </Button>
        }
      </div>

      {/* 移动端显示 FeaturePromotion，PC 端隐藏 */}
      {isMobile && <FeaturePromotion quotaUsage={quotaUsage} />}
    </div>
  );
};

export default WelcomeScreen;
