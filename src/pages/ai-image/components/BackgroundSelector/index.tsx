import { i18next } from '@ali/dingtalk-i18n'; import React, { useRef, useEffect, useState } from 'react';
import { Modal, Button } from 'dingtalk-design-mobile';
import { Popover } from 'dingtalk-design-desktop';
import { BackgroundOption } from '@/common/types';
import { isMobileDevice } from '@/utils/jsapi';
import './index.less';

interface BackgroundSelectorProps {
  onChange: (background: BackgroundOption | null) => void;
  disabled?: boolean;
}

const BackgroundSelector: React.FC<BackgroundSelectorProps> = ({
  onChange,
  disabled = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<BackgroundOption | null>(null);
  const [showModal, setShowModal] = useState(false);
  const isMobile = isMobileDevice();

  // Windows端鼠标滚轮横向滚动支持 - Windows mouse wheel horizontal scrolling support
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleWheel = (e: WheelEvent) => {
      // 检测是否为垂直滚动 - Check if it's vertical scrolling
      if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
        e.preventDefault(); // 阻止默认垂直滚动 - Prevent default vertical scrolling

        // 将垂直滚动转换为横向滚动 - Convert vertical scroll to horizontal scroll
        const scrollAmount = e.deltaY * 0.5; // 调整滚动速度 - Adjust scroll speed
        container.scrollLeft += scrollAmount;
      }
    };

    // 添加滚轮事件监听 - Add wheel event listener
    container.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      container.removeEventListener('wheel', handleWheel);
    };
  }, []);

  // 纹理背景选项数据 - Texture background options data
  const backgroundOptions: BackgroundOption[] = [
    {
      id: 'pic1',
      index: 1,
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01VWnU5l1VkWtjSufLQ_!!6000000002691-2-tps-216-216.png',
      title: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_ScienceFictionLight'),
      desc: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_BlackGlassBoothSphereAnd'),
    },
    {
      id: 'pic2',
      index: 2,
      imageUrl: 'https://img.alicdn.com/imgextra/i1/O1CN01TJzn7B21yKXKtYEyG_!!6000000007053-2-tps-216-216.png',
      title: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_DarkTableLampStrip'),
      desc: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_MetalCountertopVerticalLedLight'),
    },
    {
      id: 'pic3',
      index: 3,
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01qBBzTw1P4xFYea5J0_!!6000000001788-2-tps-216-216.png',
      title: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_NeonFire'),
      desc: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_AsphaltRoadReflectionBlurredColor'),
    },
    {
      id: 'pic4',
      index: 4,
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01KYVxh21Q74hRdAnY5_!!6000000001928-2-tps-216-216.png',
      title: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_BrightWoodenTable'),
      desc: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_WoodenDeskSquareAndSteps'),
    },
    {
      id: 'pic5',
      index: 5,
      imageUrl: 'https://img.alicdn.com/imgextra/i3/O1CN01poht3I1RnV0D8S3gR_!!6000000002156-2-tps-216-216.png',
      title: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_BlueBooth'),
      desc: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_BoothGoldBlueAdvanced'),
    },
    {
      id: 'pic6',
      index: 6,
      imageUrl: 'https://img.alicdn.com/imgextra/i1/O1CN01RpVT781iLQBpuUoET_!!6000000004396-2-tps-216-216.png',
      title: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_GoldInlaidRock'),
      desc: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_MetalCountertopDarkTextureRock'),
    },
    {
      id: 'pic7',
      index: 7,
      imageUrl: 'https://img.alicdn.com/imgextra/i4/O1CN01wDB7D01FkV1FxRge0_!!6000000000525-2-tps-216-216.png',
      title: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_WhiteSpace'),
      desc: i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_WhiteSpaceBackgroundLightingEffect'),
    }];


  // Handle template click - 处理模板点击
  const handleTemplateClick = (option: BackgroundOption) => {
    if (disabled) return;

    if (isMobile) {
      // Mobile: Show modal - 移动端：显示模态弹层
      setSelectedTemplate(option);
      setShowModal(true);
    } else {


      // PC: Direct fill (handled by popover button) - PC端：直接填入（由popover按钮处理）
      // This will be handled by the popover's fill button
    }
  };
  // Handle fill in action - 处理填入操作
  const handleFillIn = (option: BackgroundOption) => {
    onChange(option);
    if (isMobile) {
      setShowModal(false);
    }
  };

  // Handle cancel action - 处理取消操作
  const handleCancel = () => {
    setShowModal(false);
    setSelectedTemplate(null);
  };

  // Render background preview - 渲染背景预览
  const renderBackgroundPreview = (option: BackgroundOption) => {
    const backgroundElement =
      (
        <div
          key={option.id}
          className={`background-option ${disabled ? 'disabled' : ''}`}
          onClick={() => handleTemplateClick(option)}
        >
          <div
            className="background-preview"
            style={{
              backgroundImage: `url(${option.imageUrl})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
        </div>
      );


    // PC端：使用 Popover 悬停显示详情 - PC: Use Popover for hover details
    if (!isMobile && !disabled) {
      const popoverContent =
        (
          <div className="background-popover-content">
            <div className="popover-layout">
              <div className="popover-image">
                <img
                  src={option.imageUrl}
                  alt={option.title}
                  className="popover-preview-image"
                />

              </div>
              <div className="popover-info">
                <h4 className="popover-title">{option.title}</h4>
                <p className="popover-desc">{option.desc}</p>
                <Button
                  type="primary"
                  size="small"
                  className="popover-fill-btn"
                  onClick={() => handleFillIn(option)}
                >{i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_FillIn')}
                </Button>
              </div>
            </div>
          </div>
        );

      return (
        <Popover
          content={popoverContent}
          placement="bottom"
          trigger="hover"
          overlayClassName="background-template-popover"
        >
          {backgroundElement}
        </Popover>);
    }

    // 移动端或禁用状态：直接返回背景元素 - Mobile or disabled: return background element directly
    return backgroundElement;
  };

  return (
    <>
      <div
        ref={containerRef}
        className={`background-selector ${disabled ? 'disabled' : ''}`}
      >

        {/* 纹理背景选项 - Texture background options */}
        {backgroundOptions.map(renderBackgroundPreview)}
      </div>

      {/* Mobile Modal - 移动端模态弹层 */}
      {isMobile && selectedTemplate &&
      <Modal
        visible={showModal}
        onClose={handleCancel}
        className="background-template-modal"
        maskClosable
      >

        <div className="modal-content">
          <div className="modal-image">
            <img
              src={selectedTemplate.imageUrl}
              alt={selectedTemplate.title}
              className="modal-preview-image"
            />
          </div>
          <div className="modal-info">
            <h3 className="modal-title">{selectedTemplate.title}</h3>
            <p className="modal-desc">{selectedTemplate.desc}</p>
          </div>
          <div className="modal-actions">
            <Button
              className="modal-cancel-btn"
              onClick={handleCancel}
            >{i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_Cancel')}
            </Button>
            <Button
              type="primary"
              className="modal-fill-btn"
              onClick={() => handleFillIn(selectedTemplate)}
            >{i18next.t('j-dingtalk-web_pages_ai-image_components_BackgroundSelector_FillIn')}
            </Button>
          </div>
        </div>
      </Modal>
      }
    </>
  );
};

export default BackgroundSelector;
