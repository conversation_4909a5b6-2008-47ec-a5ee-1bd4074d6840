@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.background-selector {
  // 横向滚动布局 - Horizontal scrolling layout
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: visible; // 允许垂直方向溢出以显示放大效果 - Allow vertical overflow for scale effect
  gap: 8px;
  padding: 4px; // 为放大效果留出空间 - Space for scale effect

  // Windows端滚动优化 - Windows scrolling optimization
  scroll-behavior: smooth; // 平滑滚动 - Smooth scrolling
  overscroll-behavior-x: contain; // 防止过度滚动 - Prevent overscroll

  // 触摸设备滚动优化 - Touch device scrolling optimization
  -webkit-overflow-scrolling: touch; // iOS平滑滚动 - iOS smooth scrolling

  // 完全隐藏滚动条 - Completely hide scrollbar
  // Webkit浏览器 (Chrome, Safari, Edge) - Webkit browsers
  &::-webkit-scrollbar {
    display: none; // 完全隐藏滚动条 - Completely hide scrollbar
  }

  // Firefox浏览器 - Firefox browser
  scrollbar-width: none; // Firefox隐藏滚动条 - Hide scrollbar in Firefox

  // IE浏览器 (兼容性) - IE browser compatibility
  -ms-overflow-style: none; // IE隐藏滚动条 - Hide scrollbar in IE

  .background-option {
    width: 72px;
    height: 72px;
    cursor: pointer;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;
    overflow: visible; // 允许放大效果溢出 - Allow scale effect to overflow
    z-index: 1; // 确保悬停时在其他元素之上 - Ensure it's above other elements when hovered

    // 移动端：防止收缩 - Mobile: prevent shrinking
    @media (max-width: 768px) {
      flex-shrink: 0;
    }

    &:hover:not(.disabled) {
      transform: scale(1.05); // 轻微放大效果 - Slight scale effect
      z-index: 10; // 悬停时提升层级 - Increase z-index on hover
    }

    // 选中状态样式由内部图标处理 - Selected state handled by internal icon

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .background-preview {
      width: 72px; // 固定尺寸 72px - Fixed size 72px
      height: 72px;
      border: none; // 移除边框 - Remove border
      border-radius: 8px; // 圆角 8px - Border radius 8px
      position: relative;
      overflow: hidden;
      transition: all @common_light_motion_duration @common_light_motion_timing_function;

      .selected-indicator {
        position: absolute;
        top: 6px; // 调整位置 - Adjust position
        right: 6px;
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FF0E53;
        z-index: 1;
        box-shadow: 0 2px 4px rgba(255, 255, 255, 0, 0.2); // 添加阴影增强可见性 - Add shadow for better visibility
      }
    }
  }

  &.disabled {
    .background-option {
      cursor: not-allowed;

      &:hover {
        transform: none;
      }
    }
  }
}

// PC端 Popover 样式 - PC Popover styles
.background-template-popover {
  box-shadow: 0px 0px 0px 0.5px rgba(0, 0, 0, 0.06),0px 0px 16px 0px rgba(43, 44, 51, 0.06),1px 2px 60px 0px rgba(43, 44, 51, 0.14);
  padding-bottom: 0;

  .dtd-popover-inner {
    border-radius: 10px;
  }

  .dtd-popover-inner-content {
    padding: 0;
  }

  .background-popover-content {
    border-radius: 10px;
    overflow: hidden;

    .popover-layout {
      display: flex;
      width: 400px;
    }

    .popover-image {
      flex-shrink: 0;
      width: 130px;
      height: 130px;
      border-radius: 0;
      font-size: 0;

      .popover-preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .popover-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 12px;
      gap: 8px;

      .popover-title {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
        line-height: 24px;
      }

      .popover-desc {
        margin: 0;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.5);
        line-height: 22px;
        text-align: justify;
        flex: 1;
      }

      .popover-fill-btn {
        align-self: flex-end;
        margin-top: auto;
        font-size: 14px;
        line-height: 22px;
        color: #FF0E53;
        border: none;
        padding-right: 0;
        min-width: auto;
        height: 22px;
        background: transparent;
      }
    }
  }
}

// 移动端 Modal 样式 - Mobile Modal styles
.background-template-modal {
  .dtm-modal-content {
    padding-top: 0 !important;
    background-color: #222222 !important;
    border: 0.5px solid rgba(126, 134, 142, 0) !important;
    box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16) !important;
    overflow: hidden;

    .dtm-modal-body {
      padding: 0 !important;
    }
  }

  .modal-content {
    display: flex;
    flex-direction: column;
    min-height: 400px;
  }

  .modal-image {
    flex-shrink: 0;
    width: 100%;
    height: 280px;
    overflow: hidden;
    background: rgba(126, 134, 142, 0.08);

    .modal-preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .modal-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 12px;

    .modal-title {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      line-height: 24px;
    }

    .modal-desc {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.5);
      line-height: 22px;
      margin: auto;
    }
  }

  .modal-actions {
    display: flex;
    gap: 0;
    border-top: 0.5px solid rgba(126, 134, 142, 0.16);

    .dtm-button {
      flex: 1;
      height: 44px;
      border-radius: 0;
      font-size: 16px;
      background: transparent;
      border: none;
    }

    .modal-cancel-btn {
      color: rgba(255, 255, 255, 0.9);
      border-right: 0.5px solid rgba(126, 134, 142, 0.16);
    }

    .modal-fill-btn {
      color: #FF0E53;
    }
  }
}
