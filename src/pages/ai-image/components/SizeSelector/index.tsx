import React, { useState, useRef, useEffect } from 'react';
import { CheckOutlined, DropDownArrowDownOutlined } from '@ali/ding-icons';
import './index.less';

interface SizeOption {
  id: string;
  label: string;
  ratio: string;
  width: number;
  height: number;
  icon: string;
}

interface SizeSelectorProps {
  value: string;
  onChange: (size: string) => void;
  disabled?: boolean;
}

const SizeSelector: React.FC<SizeSelectorProps> = ({
  value,
  onChange,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Size options according to design requirements
  const sizeOptions: SizeOption[] = [
    {
      id: '1:1',
      label: '800x800',
      ratio: '1:1',
      width: 800,
      height: 800,
      icon: 'https://img.alicdn.com/imgextra/i4/O1CN01kF73na1ETitbJJRtJ_!!6000000000353-2-tps-64-64.png',
    },
    {
      id: '3:4',
      label: '750x1000',
      ratio: '3:4',
      width: 750,
      height: 1000,
      icon: 'https://img.alicdn.com/imgextra/i1/O1CN01Gj244y1fei1RqE2LT_!!6000000004032-2-tps-64-64.png',
    },
    {
      id: '16:9',
      label: '1280x720',
      ratio: '16:9',
      width: 1280,
      height: 720,
      icon: 'https://img.alicdn.com/imgextra/i2/O1CN01Uvbvkq1W3lX5JdsR5_!!6000000002733-2-tps-64-64.png',
    },
  ];

  // Get current selected option
  const selectedOption = sizeOptions.find((option) => option.id === value) || sizeOptions[0];

  // Handle dropdown toggle
  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // Handle option selection
  const handleOptionSelect = (optionId: string) => {
    if (!disabled) {
      onChange(optionId);
      setIsOpen(false);
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className={`size-selector ${disabled ? 'disabled' : ''}`} ref={dropdownRef}>
      {/* Dropdown trigger */}
      <div
        className={`size-selector-trigger ${isOpen ? 'open' : ''}`}
        onClick={handleToggle}
      >
        <span className="size-selector-value">{selectedOption.label}</span>
        <DropDownArrowDownOutlined className="size-selector-arrow" />
      </div>

      {/* Dropdown options */}
      {isOpen && (
        <div className="size-selector-dropdown">
          {sizeOptions.map((option) => (
            <div
              key={option.id}
              className={`size-selector-option ${value === option.id ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option.id)}
            >
              <div className="option-content">
                <img className="option-icon" src={option.icon} alt="icon" />
                <span className="option-label">{option.label}</span>
              </div>
              {value === option.id && (
                <CheckOutlined className="option-check" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SizeSelector;
