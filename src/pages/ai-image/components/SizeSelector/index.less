@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.size-selector {
  position: relative;
  min-width: 150px;

  // Dropdown trigger
  .size-selector-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 16px;
    border-radius: 24px;
    background-color: #353638;
    cursor: pointer;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;
    min-height: 36px;

    &.open {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    .size-selector-value {
      font-size: 14px;
      line-height: 22px;
      color: rgba(255, 255, 255, 0.9);
    }

    .size-selector-arrow {
      color: rgba(255, 255, 255, 0.9);
      font-size: 16px;
      transition: transform @common_light_motion_duration @common_light_motion_timing_function;

      .open & {
        transform: rotate(180deg);
      }
    }
  }

  // Dropdown options
  .size-selector-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #353638;
    border-top: none;
    border-bottom-left-radius: 24px;
    border-bottom-right-radius: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;

    .size-selector-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color @common_light_motion_duration @common_light_motion_timing_function;

      &:hover {
        background-color: darken(#353638, 2%)
      }

      &.selected {
        background-color: darken(#353638, 5%)
      }

      .option-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .option-icon {
          width: 16px;
          height: 16px;
        }

        .option-label {
          font-size: 14px;
          line-height: 22px;
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .option-check {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
      }
    }
  }

  // Disabled state
  &.disabled {
    .size-selector-trigger {
      cursor: not-allowed;
      opacity: 0.5;

      &:hover {
        border-color: @common_line_light_color;
      }
    }
  }
}
