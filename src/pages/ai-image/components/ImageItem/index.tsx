/* eslint-disable react/prop-types */
import React, { useState, memo, useCallback } from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { Toast, Modal } from 'dingtalk-design-mobile';
import {
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  RefreshOutlined,
  DeleteOutlined,
} from '@ali/ding-icons';
import OptimizedImage from '@/components/OptimizedImage';
import { rateImage, removeImage, reGenerateImage } from '@/apis/image';
import { isDingTalk, isMobileDevice, openDualLink } from '@/utils/jsapi';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { formatCompletionTime } from '@/utils/util';
import { getAIVideoUrl } from '@/utils/env';
import { sendUT } from '@/utils/trace';
import { ImageInfo } from '../../types';
import ImagePreview from '../ImagePreview';
import './index.less';

interface ImageItemProps {
  imageInfo: ImageInfo;
  onRegenerate: (imageInfo: ImageInfo) => void;
  showPromptHeader?: boolean; // Whether to show the prompt header
  className?: string;
  progress?: number; // Image generation progress (0-100)
  loadImageList: () => void; // Load image list
  onOptimizedImageUpdate?: (regenerateResult: any, originalUuid: string) => Promise<void>;
}

const ImageItem: React.FC<ImageItemProps> = memo(
  ({
    imageInfo,
    onRegenerate,
    showPromptHeader = true,
    className = '',
    progress = 0,
    loadImageList,
    onOptimizedImageUpdate,
  }) => {
    const [isLiked, setIsLiked] = useState(imageInfo.userRating === '1');
    const [isDisliked, setIsDisliked] = useState(imageInfo.userRating === '-1');
    const [isRegenerating, setIsRegenerating] = useState(false);
    const [previewVisible, setPreviewVisible] = useState(false);
    const [previewImageUrl, setPreviewImageUrl] = useState('');

    // Check if current device is mobile
    const isMobile = isMobileDevice();

    // Handle image click to open preview
    const handleImageClick = useCallback((imageUrl: string) => {
      setPreviewImageUrl(imageUrl);
      setPreviewVisible(true);
    }, []);

    // Handle preview close
    const handlePreviewClose = useCallback(() => {
      setPreviewVisible(false);
      setPreviewImageUrl('');
    }, []);

    // Handle navigation to AI video generation page
    const handleNavigateToVideo = useCallback(
      (imageUrl: string) => {
        sendUT('aigc_picture_goto_video', {
          device: isMobile ? 'mobile' : 'pc',
          imageUrl,
        });

        // Close preview first
        handlePreviewClose();

        // Navigate to AI video page with image URL as parameter
        const url = `${getAIVideoUrl()}&image_url=${encodeURIComponent(imageUrl)}`;
        if (isDingTalk()) {
          $openLink({
            url: openDualLink(url),
          });
        } else {
          // 在浏览器环境中直接跳转
          window.open(url);
        }
      },
      [isMobile, handlePreviewClose],
    );

    // Handle like/unlike - 处理点赞/取消点赞
    const handleLike = useCallback(async () => {
      try {
        if (isLiked) {
          // Cancel like if already liked
          await rateImage({ uuid: imageInfo.uuid, rating: 0 });
          setIsLiked(false);
          Toast.success({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_VideoItem_ThePraiseIsCanceled',
            ),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });
        } else {
          sendUT('aigc_picture_like', {
            device: isMobile ? 'mobile' : 'pc',
            uuid: imageInfo.uuid,
          });

          // Like the image (and cancel dislike if exists)
          await rateImage({ uuid: imageInfo.uuid, rating: 1 });
          setIsLiked(true);
          setIsDisliked(false);
          Toast.success({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_VideoItem_ThumbsUpSuccessfully',
            ),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });
        }
      } catch (error: any) {
        // eslint-disable-next-line no-console
        console.log(error);
        Toast.fail({
          content: i18next.t(
            'j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry',
          ),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      }
    }, [isLiked, imageInfo.uuid, isMobile]);

    // Handle dislike/unlike - 处理点踩/取消点踩
    const handleDislike = useCallback(async () => {
      try {
        if (isDisliked) {
          // Cancel dislike if already disliked
          await rateImage({ uuid: imageInfo.uuid, rating: 0 });
          setIsDisliked(false);
          Toast.success({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_VideoItem_TheCancellationPointIsStepped',
            ),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });
        } else {
          sendUT('aigc_picture_unlike', {
            device: isMobile ? 'mobile' : 'pc',
            uuid: imageInfo.uuid,
          });

          // Dislike the image (and cancel like if exists)
          await rateImage({ uuid: imageInfo.uuid, rating: -1 });
          setIsDisliked(true);
          setIsLiked(false);
          Toast.success({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_VideoItem_ClickSuccessfully',
            ),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });
        }
      } catch (error: any) {
        // eslint-disable-next-line no-console
        console.log(error);
        Toast.fail({
          content: i18next.t(
            'j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry',
          ),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      }
    }, [isDisliked, imageInfo.uuid, isMobile]);

    // Optimized image update handler after regeneration
    const handleOptimizedImageUpdate = async (regenerateResult: any, originalUuid: string) => {
      // Use the optimized update function from parent if available, otherwise fallback
      if (onOptimizedImageUpdate) {
        await onOptimizedImageUpdate(regenerateResult, originalUuid);
      } else {
        // Fallback to original behavior
        loadImageList();
      }
    };

    // Handle regenerate - directly call regenerate API for failed images
    const handleRegenerate = useCallback(async () => {
      sendUT('aigc_picture_regenerate', {
        device: isMobile ? 'mobile' : 'pc',
        uuid: imageInfo.uuid,
      });

      // For failed images, directly regenerate without going to form
      if (imageInfo.status === 'failed') {
        if (isRegenerating) return;

        setIsRegenerating(true);

        try {
          Toast.info({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_VideoItem_RegeneratingVideo',
            ),
            duration: 3,
            position: 'top',
            maskClickable: true,
          });

          // Call regenerate API with the original image's UUID
          const result = await reGenerateImage({
            fromUuid: imageInfo.uuid,
          });

          if (result?.success) {
            Toast.success({
              content: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationHasStartedPleaseWait',
              ),
              duration: 2,
              position: 'top',
              maskClickable: true,
            });

            // Optimized: Use targeted status check instead of full list reload
            await handleOptimizedImageUpdate(result, imageInfo.uuid);
          } else {
            throw new Error(
              result?.errorMsg ||
                i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailed'),
            );
          }
        } catch (error) {
          // 如果 reGenerateImage 返回 success: false, 则需要显示 errorMsg 信息
          Toast.fail({
            content:
              error instanceof Error
                ? error?.message
                : i18next.t(
                  'j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailedPleaseTryAgain',
                ),
            duration: 3,
            position: 'top',
            maskClickable: true,
          });
        } finally {
          setIsRegenerating(false);
        }
        return;
      }

      // For other cases, use the original callback behavior
      if (onRegenerate) {
        onRegenerate(imageInfo);
      }
    }, [imageInfo, isRegenerating, onRegenerate, isMobile, handleOptimizedImageUpdate]);

    // Handle remove - 删除图片
    const handleRemove = useCallback(async () => {
      if (!imageInfo.uuid) {
        return;
      }

      // 删除操作之前需要先弹框确认，使用 dingtalk-design-mobile 的 Modal
      Modal.alert(
        i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConfirmDeletion'),
        i18next.t('j-dingtalk-web_pages_ai-image_components_ImageItem_AreYouSureYouWant'),

        [
          {
            text: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Cancel'),
            onClick: () => {
              // User cancelled deletion - no action needed
            },
          },
          {
            text: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Delete'),
            style: { color: '#FF0E53' },
            onClick: async () => {
              try {
                const result = await removeImage({ uuid: imageInfo.uuid });
                if (result?.success) {
                  Toast.success({
                    content: i18next.t(
                      'j-dingtalk-web_pages_aiVideo_components_VideoItem_DeletedSuccessfully',
                    ),
                    duration: 2,
                    position: 'top',
                    maskClickable: true,
                  });
                  // Delete successfully, refresh image list
                  loadImageList();
                } else {
                  // Handle API response with success: false
                  Toast.fail({
                    content:
                      result?.errorMsg ||
                      i18next.t(
                        'j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry',
                      ),
                    duration: 3,
                    position: 'top',
                    maskClickable: true,
                  });
                }
              } catch (error: any) {
                // eslint-disable-next-line no-console
                console.log(error);
                Toast.fail({
                  content: i18next.t(
                    'j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry',
                  ),
                  duration: 3,
                  position: 'top',
                  maskClickable: true,
                });
              }
            },
          },
        ],
      );
    }, [imageInfo.uuid, loadImageList]);

    const currentProgress = progress || 0;
    const isGenerating = imageInfo.status === 'pending' || imageInfo.status === 'processing';
    const isFailed = imageInfo.status === 'failed';
    const isCompleted = imageInfo.status === 'finish';

    return (
      <div className={`image-item ${className}`}>
        {/* Status Header - shows different content based on status */}
        {showPromptHeader && (
          <div className="image-item-header">
            <div className="status-info-container">
              {isGenerating && (
                <div className="status-info generating">
                  <span className="status-text">
                    {i18next.t(
                      'j-dingtalk-web_pages_ai-image_components_ImageItem_TheImageIsBeingGenerated',
                    )}
                  </span>
                  <span className="progress-text">{Math.round(currentProgress)}%</span>
                </div>
              )}
              {isCompleted && (
                <div className="status-info completed">
                  <span className="status-text">
                    {i18next.t(
                      'j-dingtalk-web_pages_ai-image_components_ImageItem_CompletedPleaseCheck',
                    )}
                  </span>
                </div>
              )}
              {isFailed && (
                <div className="status-info failed">
                  <span className="status-text">
                    {i18next.t(
                      'j-dingtalk-web_pages_ai-image_components_ImageItem_SorryTheGenerationFailed',
                    )}
                  </span>
                  <button className="action-button" onClick={handleRegenerate}>
                    <span className="error-text">
                      {i18next.t('j-dingtalk-web_pages_ai-image_components_ImageItem_TryAgain')}
                    </span>
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Image Content */}
        <div className="image-content">
          {isCompleted && (
            <div className="image-container">
              <div className="image-grid">
                {imageInfo.imgUrls?.slice(0, 4).map((url) => (
                  <OptimizedImage
                    key={`${imageInfo.uuid}-${url}`}
                    src={url}
                    alt="Generated image"
                    className="image-preview clickable"
                    width={168}
                    height={150}
                    fit="cover"
                    lazy // Enable lazy loading for better performance
                    format="webp"
                    referrerPolicy="no-referrer"
                    onClick={() => handleImageClick(url)}
                  />
                ))}
              </div>
            </div>
          )}

          {isGenerating && (
            <div className="image-placeholder">
              <div className="image-grid-generating">
                {/* Create 4 placeholder items for generation preview */}
                {Array.from({ length: 4 }, (_, index) => (
                  <div
                    key={`placeholder-${index}`}
                    className={`image-placeholder-item generating item-${index}`}
                    style={{
                      backgroundImage: imageInfo.originImgUrl
                        ? `url(${imageInfo.originImgUrl})`
                        : 'none',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      backgroundRepeat: 'no-repeat',
                      // Use transform3d for hardware acceleration
                      transform: 'translate3d(0, 0, 0)',
                    }}
                  >
                    {/* Frosted glass gradient mask overlay */}
                    <div
                      className="generation-mask frosted-glass"
                      style={{
                        // Consistent frosted glass effect for all 4 images
                        // Start from completely opaque (1.0) to slightly transparent (0.2) at 95%
                        opacity: Math.max(0.2, Math.min(1.0, (100 - currentProgress) / 100)),
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {isFailed && (
            <div className="image-placeholder">
              <div className="single-placeholder-container">
                <div
                  className="image-placeholder-item failed"
                  style={{
                    backgroundImage: imageInfo.originImgUrl
                      ? `url(${imageInfo.originImgUrl})`
                      : 'url(https://img.alicdn.com/imgextra/i2/O1CN01jWzF531SMlV1ZQMkt_!!6000000002233-2-tps-672-600.png)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                    filter: 'grayscale(50%) brightness(0.7)',
                  }}
                />

                <div className="failed-overlay">
                  <span className="failed-text">
                    {i18next.t(
                      'j-dingtalk-web_pages_ai-image_components_ImageItem_FailedToGenerate',
                    )}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons - show when image is generated or failed */}
        {(isCompleted || isFailed) && (
          <>
            {/* PC Layout: Action buttons */}
            {!isMobile && (
              <div className="action-section pc-layout">
                <div className="action-buttons">
                  {isCompleted && (
                    <>
                      <button
                        className={`action-button ${isLiked ? 'liked' : ''}`}
                        onClick={handleLike}
                      >
                        {isLiked ? <LikeFilled /> : <LikeOutlined />}
                      </button>
                      <button
                        className={`action-button ${isDisliked ? 'disliked' : ''}`}
                        onClick={handleDislike}
                      >
                        {isDisliked ? <DislikeFilled /> : <DislikeOutlined />}
                      </button>
                    </>
                  )}
                  {(isFailed || isCompleted) && onRegenerate && (
                    <button
                      className={`action-button ${isRegenerating ? 'loading' : ''}`}
                      onClick={handleRegenerate}
                      disabled={isRegenerating}
                    >
                      <RefreshOutlined />
                    </button>
                  )}
                  <button className="action-button" onClick={handleRemove}>
                    <DeleteOutlined />
                  </button>
                </div>
                {imageInfo.finishTime && imageInfo.status === 'finish' && (
                  <div className="completion-time">
                    {formatCompletionTime(new Date(imageInfo.finishTime))}
                  </div>
                )}
              </div>
            )}

            {/* Mobile Layout: Action buttons */}
            {isMobile && (
              <div className="action-section pc-layout">
                <div className="action-buttons">
                  {isCompleted && (
                    <>
                      <button
                        className={`action-button ${isLiked ? 'liked' : ''}`}
                        onClick={handleLike}
                      >
                        {isLiked ? <LikeFilled /> : <LikeOutlined />}
                      </button>
                      <button
                        className={`action-button ${isDisliked ? 'disliked' : ''}`}
                        onClick={handleDislike}
                      >
                        {isDisliked ? <DislikeFilled /> : <DislikeOutlined />}
                      </button>
                    </>
                  )}
                  {(isFailed || isCompleted) && onRegenerate && (
                    <button
                      className={`action-button ${isRegenerating ? 'loading' : ''}`}
                      onClick={handleRegenerate}
                      disabled={isRegenerating}
                    >
                      <RefreshOutlined />
                    </button>
                  )}
                  <button className="action-button" onClick={handleRemove}>
                    <DeleteOutlined />
                  </button>
                </div>
                {imageInfo.finishTime && imageInfo.status === 'finish' && (
                  <div className="completion-time mobile-layout">
                    {formatCompletionTime(new Date(imageInfo.finishTime))}
                  </div>
                )}
              </div>
            )}
          </>
        )}

        {/* Image Preview Modal */}
        <ImagePreview
          visible={previewVisible}
          imageUrl={previewImageUrl}
          onClose={handlePreviewClose}
          onNavigateToVideo={handleNavigateToVideo}
        />
      </div>
    );
  },
);

ImageItem.displayName = 'ImageItem';

export default ImageItem;
