.image-item {
  width: 100%;
  background-color: #000000;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  .image-item-header {
    padding: 16px 0 10px 0;

    .status-info-container {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .status-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .status-text {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          line-height: 22px;
          font-weight: 500;
        }

        .progress-text {
          color: rgba(255, 255, 255, 0.7);
          font-size: 16px;
          line-height: 22px;
        }

        .progress-indicator {
          .circular-progress {
            display: inline-block;
            width: 24px;
            height: 24px;

            .progress-ring {
              width: 24px;
              height: 24px;
              transform: rotate(-90deg);

              .progress-ring-circle {
                transition: stroke-dashoffset 0.3s ease;
                stroke: white;
              }
            }
          }
        }

        .error-text {
          color: #FF0E53;
          font-size: 16px;
          line-height: 22px;
          margin-left: 16px;
        }
      }
    }
  }

  .image-content {
    position: relative;
    width: 100%;
    height: 316px; // Fixed height to accommodate 4-image grid: 150px * 2 + 8px + padding
    border-radius: 12px;
    overflow: hidden;

    .image-container {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .image-grid {
        display: grid;
        grid-template-columns: 168px 168px;
        grid-template-rows: 150px 150px;
        gap: 8px;

        .image-preview {
          width: 168px;
          height: 150px;
          object-fit: cover;

          &.clickable {
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              transform: scale(1.02);
              filter: brightness(1.1);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            &:active {
              transform: scale(0.98);
            }
          }
        }
      }
    }

    .image-placeholder {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      // Optimized single placeholder container for better performance
      .single-placeholder-container {
        position: relative;
        width: 344px; // Full width to match image grid
        height: 308px; // Full height to match image grid
        border-radius: 12px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;

        .image-placeholder-item {
          width: 100%;
          height: 100%;
          border-radius: 12px;
          overflow: hidden;
          position: absolute;
          top: 0;
          left: 0;
          // Optimize for performance
          backface-visibility: hidden;
          -webkit-backface-visibility: hidden;

          &.generating {
            // Background image will be set via inline styles
            // Add a subtle overlay to enhance the blur effect
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.2);
              z-index: 1;
            }
          }

          &.failed {
            // Add a subtle overlay to indicate failed state
            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(255, 0, 0, 0.1);
              z-index: 1;
            }
          }
        }

        // Failed overlay
        .failed-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 2;
          background: rgba(255, 0, 0, 0.8);
          padding: 8px 16px;
          border-radius: 20px;

          .failed-text {
            color: white;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }

      // Grid layout for generation preview with 4 tiles
      .image-grid-generating {
        display: grid;
        grid-template-columns: 168px 168px;
        grid-template-rows: 150px 150px;
        gap: 8px;

        .image-placeholder-item {
          width: 168px;
          height: 150px;
          overflow: hidden;
          position: relative;
          // Optimize for performance
          backface-visibility: hidden;
          -webkit-backface-visibility: hidden;

          &.generating {
            // Generation mask overlay with frosted glass effect only
            .generation-mask {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              transition: opacity 0.5s ease-out;
              z-index: 1;
              
              &.frosted-glass {
                // Strong frosted glass background to completely hide the underlying image
                background: linear-gradient(
                  135deg,
                  rgba(255, 255, 255, 0.8) 0%,
                  rgba(255, 255, 255, 0.6) 25%,
                  rgba(255, 255, 255, 0.5) 50%,
                  rgba(255, 255, 255, 0.6) 75%,
                  rgba(255, 255, 255, 0.7) 100%
                );
                
                // Very strong backdrop blur to completely obscure the image initially
                backdrop-filter: blur(20px) saturate(1.8);
              }
            }
          }
        }
      }
    }
  }

  // Action buttons section
  .action-section.pc-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;

    .action-buttons {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 24px;
    }

    .completion-time {
      text-align: right;
      color: rgba(255, 255, 255, 0.4);
      font-size: 16px;
      line-height: 22px;
      flex-shrink: 0;
      margin-left: 16px;
    }
  }

  .completion-time.mobile-layout {
    text-align: left;
    color: rgba(255,255,255,0.4);
    font-size: 16px;
    line-height: 22px;
  }

  .action-button {
    background-color: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.9);
    font-size: 24px;

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    // Like button active state
    &.liked {
      color: #ffffff;
    }

    // Dislike button active state
    &.disliked {
      color: #ffffff;
    }
  }
}
