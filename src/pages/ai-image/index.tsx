import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { Toast } from 'dingtalk-design-mobile';
import theme, { IThemeType } from 'dingtalk-theme';
import { isDingTalk, isMobileDevice, setPageTitle, openDualLink } from '@/utils/jsapi';
import { sendUT, initPageTimeTracking, PageTimeTracker } from '@/utils/trace';
import { safeJsonParseArray, formatTimestampToISO } from '@/utils/parseUtils';
import { generateImage, checkImageStatus, listImages } from '@/apis/image';
import { getOrderListUrl, getCreateOrderUrl } from '@/utils/env';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { EditOutlined, ListViewOutlined, MoreOutlined } from '@ali/ding-icons';
import Loading from '@/components/Loading';
import MobileNavbar from '@/components/MobileNavbar';
import FreeModal from '@/components/FreeModal';
import useQuotaUsage from '@/hooks/useQuotaUsage';
import useFreeQuota from '@/hooks/useFreeQuota';
import { log } from '@/utils/console';
import WelcomeScreen from './components/WelcomeScreen';
import ImageForm from './components/ImageForm';
import ImageList from './components/ImageList';
import { ImageInfo, AIImageState } from './types';
import './index.less';

theme.setTheme(IThemeType.dark);

// Helper function to get image pixel size
const getImagePixel = (size: string): string => {
  switch (size) {
    case '1:1':
      return '800x800';
    case '3:4':
      return '750x1000';
    case '16:9':
      return '1280x720';
    default:
      return '800x800';
  }
};

// Helper function to convert pixel size to ratio
const getImageRatio = (pixel: string): string => {
  switch (pixel) {
    case '800x800':
      return '1:1';
    case '750x1000':
      return '3:4';
    case '1280x720':
      return '16:9';
    default:
      return '1:1';
  }
};

const AIImagePage: React.FC = () => {
  const [state, setState] = useState<AIImageState>({
    currentStep: isMobileDevice() ? 'welcome' : 'form',
    rightPanelContent: 'welcome', // PC layout right panel starts with welcome
    uploadedImage: null,
    positivePrompt: '',
    imageSize: '1:1',
    isGenerating: false,
    generatedImageUrl: null,
    error: null,
    taskId: '',
    progress: 0,
    // Image list related states
    imageList: [],
    isLoadingImages: true, // Start with loading state
    hasImages: false,
    imageListError: null,
    nextUuid: null,
    hasNextPage: false,
    progressMap: {},
    isLoadingMore: false, // Loading state for pagination
  });

  const pollingTimersRef = useRef<Record<string, ReturnType<typeof setTimeout>>>({});
  const isUnmountedRef = useRef(false);
  const timeTrackerRef = useRef<PageTimeTracker | null>(null);
  // Track consecutive 500 errors for each polling image
  const consecutiveErrorsRef = useRef<Record<string, number>>({});

  // 使用 useQuotaUsage hook 管理额度数据
  const { quotaUsage, refreshQuotaUsage, updateUsedQuota } = useQuotaUsage('ai_material');

  // 使用 useFreeQuota hook 管理免费额度
  const {
    freeModalVisible,
    quotaAccessData,
    freeModalLoading,
    checkFreeQuota,
    handleFreeModalClose,
    handleFreeModalReceive: originalHandleFreeModalReceive,
  } = useFreeQuota({
    resourceKey: 'ai_material',
    eventPrefix: 'aigc_picture',
    errorMessages: {
      obtainFailed: i18next.t('j-dingtalk-web_pages_ai-image_FailedToObtainFreeQuota'),
      interfaceAbnormal: i18next.t('j-dingtalk-web_pages_ai-image_TheInterfaceIsAbnormalPlease'),
      collectFailed: i18next.t('j-dingtalk-web_pages_ai-image_FailedToCollectPleaseTry'),
    },
  });

  // 包装 handleFreeModalReceive 来传递 refreshQuotaUsage 函数
  const handleFreeModalReceive = async () => {
    await originalHandleFreeModalReceive(refreshQuotaUsage);
  };

  // Calculate progress based on time elapsed since image creation
  const calculateTimeBasedProgress = (createdAt: string): number => {
    try {
      const createdTime = new Date(createdAt).getTime();
      const currentTime = Date.now();
      const elapsedSeconds = (currentTime - createdTime) / 1000;

      // Estimated total generation time: 120 seconds (2 minutes for images)
      const estimatedTotalSeconds = 120;

      // Calculate progress percentage
      let progress = (elapsedSeconds / estimatedTotalSeconds) * 100;

      // Ensure progress is between 2% and 95%
      progress = Math.max(2, Math.min(95, progress));

      return Math.round(progress);
    } catch {
      // If timestamp parsing fails, return minimum progress
      return 2;
    }
  };

  // Utility function to scroll to top of image list
  const scrollToTop = () => {
    setTimeout(() => {
      if (isMobileDevice()) {
        // Mobile: scroll window to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        // PC: scroll right panel to top
        const rightPanel = document.querySelector('.ai-image-right-panel') as HTMLElement;
        if (rightPanel) {
          rightPanel.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
    }, 100); // Small delay to ensure DOM is updated
  };





  useEffect(() => {
    // Set page title
    setPageTitle(i18next.t('j-dingtalk-web_pages_ai-image_AiImageGeneration'));

    // Check free quota access
    checkFreeQuota();

    // Send page view event
    sendUT('aigc_picture_homepage_exposure', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });

    // Initialize page time tracking
    timeTrackerRef.current = initPageTimeTracking('aigc_picture_generate_time');

    // Load image list on component mount
    loadImageList();

    // Enhanced cleanup on unmount
    return () => {
      isUnmountedRef.current = true;

      // Cleanup time tracking
      if (timeTrackerRef.current) {
        timeTrackerRef.current.cleanup();
        timeTrackerRef.current = null;
      }

      // Use centralized cleanup function
      cleanupAllPolling();
    };
  }, []);

  // Handle share button click
  const handleShareClick = () => {
    if (isDingTalk()) {
      $setShare({
        type: 0,
        url: window.location.href,
        title: i18next.t('j-dingtalk-web_pages_ai-image_AiImageGeneration'),
        content: i18next.t('j-dingtalk-web_pages_ai-image_TheBackgroundReplacementAndAutomatic'),
        image:
          'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
      });
    }
  };



  // Handle create new image button click (for ImageList step)
  const handleCreateNewClick = () => {
    handleCreateNewImage();
  };

  // Handle navigate to list button click (for ImageForm step)
  const handleNavigateToListClick = () => {
    handleStepChange('imageList');
  };

  // Handle order list button click
  const handleOrderListClick = () => {
    const url = getOrderListUrl();
    if (isDingTalk()) {
      $openLink({
        url: openDualLink(url),
      });
    } else {
      window.open(url);
    }
  };

  // Dynamic navbar configuration based on current step
  const navbarConfig = useMemo(() => {
    switch (state.currentStep) {
      case 'welcome':
        // WelcomeScreen: show share button
        return {
          showOrderButton: true,
          showShareButton: true,
          onOrderClick: handleOrderListClick,
          onShareClick: handleShareClick,
          shareConfig: {
            url: window.location.href,
            title: i18next.t('j-dingtalk-web_pages_ai-image_AiImageGeneration'),
            content: i18next.t(
              'j-dingtalk-web_pages_ai-image_TheBackgroundReplacementAndAutomatic',
            ),
            image:
              'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
          },
        };
      case 'form':
        // ImageForm: show list button and share button
        return {
          customRightButtons: [
            {
              icon: <ListViewOutlined />,
              text: i18next.t('j-dingtalk-web_pages_ai-image_List'),
              onClick: handleNavigateToListClick,
            },
            {
              icon: <MoreOutlined />,
              text: i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_Share'),
              onClick: handleShareClick,
            },
          ],
        };
      case 'imageList':
        // ImageList: show create button (edit icon)
        return {
          customRightButtons: [
            {
              icon: <EditOutlined />,
              text: i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_Create'),
              onClick: handleCreateNewClick,
            },
          ],
        };
      default:
        // Default: show share button
        return {
          showShareButton: true,
          onShareClick: handleShareClick,
        };
    }
  }, [state.currentStep]);

  // Handle form updates
  const handleFormUpdate = (updates: Partial<AIImageState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  // Handle step changes for mobile
  const handleStepChange = (
    step: 'welcome' | 'form' | 'imageList',
    e?: React.MouseEvent<HTMLButtonElement>,
  ) => {
    e?.preventDefault();
    e?.stopPropagation();
    setState((prev) => ({ ...prev, currentStep: step }));
  };

  // Handle create new image
  const handleCreateNewImage = () => {
    if (isMobileDevice()) {
      setState((prev) => ({
        ...prev,
        currentStep: 'form',
        // Clear form fields for a fresh start
        uploadedImage: null,
        positivePrompt: '',
        imageSize: '1:1',
        error: null, // Clear any previous errors
      }));
    } else {
      setState((prev) => ({
        ...prev,
        rightPanelContent: 'welcome',
        // Clear form fields for a fresh start
        uploadedImage: null,
        positivePrompt: '',
        imageSize: '1:1',
        error: null, // Clear any previous errors
      }));
    }
  };

  // Load image list
  const loadImageList = async (isLoadMore = false) => {
    try {
      if (!isLoadMore) {
        setState((prev) => ({ ...prev, isLoadingImages: true, imageListError: null }));
      } else {
        setState((prev) => ({ ...prev, isLoadingMore: true, imageListError: null }));
      }

      const result = await listImages({
        limit: 20,
      }) as any;

      if (result && result.success && result.datas) {
        // Parse datas from stringified format
        const datasArray = safeJsonParseArray(result.datas, []);

        const imageList: ImageInfo[] = datasArray.map((item: any) => {
          // Parse targetImgUrl from stringified format
          const imgUrls = safeJsonParseArray(item.targetImgUrl, []);

          return {
            uuid: item.uuid,
            imgUrls,
            originImgUrl: item.sourceImgUrl || '',
            type: item.type || 'normal',
            userRating: item.userRating || '0',
            status: item.status || 'pending',
            createdAt: item.createdAt
              ? new Date(item.createdAt).toISOString()
              : new Date().toISOString(),
            finishTime: item.finishTime ? new Date(item.finishTime).toISOString() : '',
            requestId: item.requestId || item.uuid || '',
            // Additional fields from API
            requirements: item.requirements || '',
            imgPixel: item.imgPixel || '',
            operator: item.operator || '',
            updatedAt: item.updatedAt ? new Date(item.updatedAt).toISOString() : '',
            // Set prompt from requirements for display
            prompt: item.requirements || '',
            imageSize: getImageRatio(item.imgPixel || '800x800'),
          };
        });

        const hasImages = imageList.length > 0;
        const isMobile = isMobileDevice();

        // Determine initial step based on device and video availability
        let initialStep: AIImageState['currentStep'];
        if (isMobile) {
          initialStep = hasImages ? 'imageList' : 'welcome';
        } else {
          initialStep = 'form';
        }

        setState((prev) => {
          // Calculate initial progress for processing images based on creation time
          const initialProgressMap: Record<string, number> = {};
          imageList.forEach((image) => {
            if ((image.status === 'processing' || image.status === 'pending') && image.createdAt) {
              // Get existing progress to ensure we don't decrease it
              const existingProgress = prev.progressMap[image.uuid] || 0;
              const timeBasedProgress = calculateTimeBasedProgress(image.createdAt);
              // Use the higher value to ensure progress never decreases
              initialProgressMap[image.uuid] = Math.max(existingProgress, timeBasedProgress);
            }
          });

          // Preserve progress for images that are still generating but might not be in the new list
          const preservedProgressMap: Record<string, number> = {};
          Object.entries(prev.progressMap).forEach(([uuid, progress]) => {
            // Keep progress for images that are not in the new list but still generating
            const imageInNewList = imageList.find((img) => img.uuid === uuid);
            if (!imageInNewList && progress > 0 && progress < 100) {
              // Check if this image still exists in our current list and is generating
              const imageInCurrentList = prev.imageList.find((img) => img.uuid === uuid);
              if (
                imageInCurrentList &&
                (imageInCurrentList.status === 'processing' ||
                  imageInCurrentList.status === 'pending')
              ) {
                preservedProgressMap[uuid] = progress;
              }
            }
          });

          return {
            ...prev,
            imageList: isLoadMore ? [...prev.imageList, ...imageList] : imageList,
            hasImages,
            isLoadingImages: false,
            isLoadingMore: false,
            nextUuid: result.nextUuid || null, // Set nextUuid for pagination
            hasNextPage: result.hasNext || false,
            // Merge all progress maps: existing + preserved + new
            progressMap: { ...prev.progressMap, ...preservedProgressMap, ...initialProgressMap },
            currentStep: initialStep,
            rightPanelContent: hasImages ? 'imageList' : 'welcome',
          };
        });

        if (hasImages) {
          scrollToTop();
        }

        // Start polling for images that need it
        checkAndStartPollingForImages(imageList);
      } else {
        // Handle empty image list - preserve progressMap for ongoing generations
        setState((prev) => ({
          ...prev,
          imageList: [],
          hasImages: false,
          isLoadingImages: false,
          isLoadingMore: false,
          nextUuid: null,
          hasNextPage: false,
          currentStep: isMobileDevice() ? 'welcome' : 'form',
          rightPanelContent: 'welcome',
          // Keep progressMap to preserve ongoing generation progress
        }));
      }
    } catch (error) {
      // Handle API error silently, log for debugging if needed
      setState((prev) => ({
        ...prev,
        isLoadingImages: false,
        isLoadingMore: false,
        imageListError:
          error instanceof Error
            ? error.message
            : i18next.t('j-dingtalk-web_pages_ai-image_FailedToObtainTheImage'),
        hasImages: false,
        // Keep original welcome/form logic when API fails
        currentStep: isMobileDevice() ? 'welcome' : 'form',
        rightPanelContent: 'welcome',
        // Preserve progressMap to maintain ongoing generation progress
      }));
    }
  };

  // Load more images for pagination
  const loadMoreImages = async () => {
    if (state.isLoadingMore || !state.hasNextPage || !state.nextUuid) {
      return;
    }

    setState((prev) => ({ ...prev, isLoadingMore: true }));

    try {
      const result = await listImages({
        limit: 20,
        nextUuid: state.nextUuid,
      }) as any;

      if (result && result.success && result.datas) {
        // Parse datas from stringified format
        const datasArray = safeJsonParseArray(result.datas, []);
        const newImageList: ImageInfo[] = datasArray.map((item: any) => {
          // Parse targetImgUrl from stringified format
          const imgUrls = safeJsonParseArray(item.targetImgUrl, []);

          return {
            uuid: item.uuid,
            imgUrls,
            originImgUrl: item.sourceImgUrl || '',
            type: item.type || 'normal',
            userRating: item.userRating || '0',
            status: item.status || 'pending',
            createdAt: item.createdAt
              ? new Date(item.createdAt).toISOString()
              : new Date().toISOString(),
            finishTime: item.finishTime ? new Date(item.finishTime).toISOString() : '',
            requestId: item.requestId || item.uuid || '',
            // Additional fields from API
            requirements: item.requirements || '',
            imgPixel: item.imgPixel || '',
            operator: item.operator || '',
            updatedAt: item.updatedAt ? new Date(item.updatedAt).toISOString() : '',
            // Set prompt from requirements for display
            prompt: item.requirements || '',
            imageSize: getImageRatio(item.imgPixel || '800x800'),
          };
        });

        setState((prev) => ({
          ...prev,
          imageList: [...prev.imageList, ...newImageList],
          hasImages: prev.imageList.length + newImageList.length > 0,
          nextUuid: result.nextUuid || null,
          hasNextPage: result.hasNext || false,
          isLoadingMore: false,
        }));

        // Start polling for images that need it
        checkAndStartPollingForImages(newImageList);
      } else {
        // Handle empty image list
        setState((prev) => ({
          ...prev,
          isLoadingMore: false,
          nextUuid: null,
          hasNextPage: false,
        }));
      }
    } catch (error: any) {
      log('error', error.message);
      // Handle API error
      setState((prev) => ({
        ...prev,
        isLoadingMore: false,
      }));
    }
  };

  // Refresh image list
  const handleRefreshImageList = () => {
    loadImageList();
  };

  // Enhanced cleanup for polling timers
  const stopPollingForImage = useCallback((uuid: string) => {
    if (pollingTimersRef.current[uuid]) {
      clearTimeout(pollingTimersRef.current[uuid]);
      delete pollingTimersRef.current[uuid];
    }
    // Clean up consecutive error count for this image
    if (consecutiveErrorsRef.current[uuid]) {
      delete consecutiveErrorsRef.current[uuid];
    }
  }, []);

  // Cleanup all polling timers
  const cleanupAllPolling = useCallback(() => {
    Object.values(pollingTimersRef.current).forEach((timer) => {
      if (timer) clearTimeout(timer);
    });
    pollingTimersRef.current = {};
    // Clean up all consecutive error counts
    consecutiveErrorsRef.current = {};
  }, []);

  // Generic polling function for image status (consistent with video page logic)
  const pollImageStatus = async (
    uuid: string,
    pollCount = 0,
    isCurrentlyGenerating = false,
  ): Promise<void> => {
    const maxPollCount = 60; // Maximum 60 polls (2 minutes total)
    const pollInterval = 2000; // 2 seconds interval

    try {
      const currentPollCount = pollCount + 1;

      // Calculate progress: ensure smooth progression from current to next value
      let progressPercentage: number;

      setState((prev) => {
        // Get current progress for this image
        const existingProgress = prev.progressMap[uuid] || 0;

        // Find the image to get its creation time for time-based calculation
        const imageInfo = prev.imageList.find((img) => img.uuid === uuid);
        if (imageInfo && imageInfo.createdAt) {
          // Use time-based progress calculation as primary method
          const timeBasedProgress = calculateTimeBasedProgress(imageInfo.createdAt);
          // Ensure progress only increases, never decreases
          progressPercentage = Math.max(existingProgress, timeBasedProgress);
        } else {
          // Fallback: use poll-based progress but ensure it doesn't decrease
          const pollBasedProgress = Math.min((currentPollCount / maxPollCount) * 90, 90);
          progressPercentage = Math.max(existingProgress, pollBasedProgress);
        }

        // After timeout (maxPollCount reached), keep progress at 95% minimum
        if (currentPollCount >= maxPollCount) {
          progressPercentage = Math.max(95, existingProgress);
        } else {
          // Before timeout, ensure progress is within valid range and never decreases
          progressPercentage = Math.max(existingProgress, Math.min(progressPercentage, 95));
        }

        return {
          ...prev,
          progress: isCurrentlyGenerating ? progressPercentage : prev.progress,
          progressMap: {
            ...prev.progressMap,
            [uuid]: progressPercentage,
          },
        };
      });

      const result = await checkImageStatus({ uuid }) as any;

      // Reset consecutive error count on successful API call
      consecutiveErrorsRef.current[uuid] = 0;

      // Check if status is success with imgUrls (using new API format)
      if (result.status === 'finish' && result.imgUrls && result.imgUrls.length > 0) {
        // Parse imgUrls array using utility function
        const parsedImgUrls = result.imgUrls;
        stopPollingForImage(uuid);
        setState((prev) => {
          const newProgressMap = { ...prev.progressMap };
          delete newProgressMap[uuid];
          return {
            ...prev,
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            generatedImageUrl: isCurrentlyGenerating ? parsedImgUrls[0] : prev.generatedImageUrl,
            progress: isCurrentlyGenerating ? 100 : prev.progress,
            progressMap: newProgressMap,
            // Update the image in the list
            imageList: prev.imageList.map((image) =>
              (image.uuid === uuid
                ? {
                  ...image,
                  imgUrls: parsedImgUrls,
                  finishTime: formatTimestampToISO(result.finishTime, image.finishTime),
                  status: 'finish', // Keep as 'finish' to match API response
                }
                : image)),
          };
        });
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_ai-image_TheImageIsGeneratedSuccessfully'),
          position: 'top',
          maskClickable: true,
          duration: 2,
        });
        return;
      }

      // Check if status is failed
      if (result.status === 'failed') {
        stopPollingForImage(uuid);
        setState((prev) => {
          const newProgressMap = { ...prev.progressMap };
          delete newProgressMap[uuid];
          return {
            ...prev,
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            error: isCurrentlyGenerating
              ? result.errorMsg || i18next.t('j-dingtalk-web_pages_ai-image_ImageGenerationFailed')
              : prev.error,
            // Only reset progress to 0 for actual API failures
            progress: isCurrentlyGenerating ? 0 : prev.progress,
            progressMap: newProgressMap,
            // Update the image status in the list
            imageList: prev.imageList.map((image) =>
              (image.uuid === uuid
                ? { ...image, status: 'failed', errorMsg: result.errorMsg }
                : image)),
          };
        });
        Toast.fail({
          content:
            result.errorMsg ||
            i18next.t('j-dingtalk-web_pages_ai-image_ImageGenerationFailedPleaseTry'),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
        return;
      }

      // If status is pending or processing, continue polling
      if (result.status === 'pending' || result.status === 'processing') {
        // Check if we've reached the maximum poll count for initial timeout warning
        if (currentPollCount >= maxPollCount) {
          // Set progress to 95% and keep polling (don't stop)
          setState((prev) => {
            return {
              ...prev,
              progress: isCurrentlyGenerating ? 95 : prev.progress,
              progressMap: {
                ...prev.progressMap,
                [uuid]: 95,
              },
              // Keep image status as processing, don't mark as failed
              imageList: prev.imageList.map((image) =>
                (image.uuid === uuid ? { ...image, status: 'processing' } : image)),
            };
          });

          // Show timeout warning but continue processing
          if (currentPollCount === maxPollCount) {
            Toast.info({
              content: i18next.t('j-dingtalk-web_pages_ai-image_ImageGenerationTakingLongerThan'),
              position: 'top',
              maskClickable: true,
              duration: 3,
            });
          }
        }

        // Continue polling after interval (no matter the poll count)
        // Check if component is still mounted before setting new timer
        if (!isUnmountedRef.current) {
          const timer = setTimeout(() => {
            if (!isUnmountedRef.current) {
              pollImageStatus(uuid, currentPollCount, isCurrentlyGenerating);
            }
          }, pollInterval);
          pollingTimersRef.current[uuid] = timer;
        }
        return;
      }

      // Handle unexpected status
      stopPollingForImage(uuid);
      setState((prev) => {
        const existingProgress = prev.progressMap[uuid] || 0;
        const newProgressMap = { ...prev.progressMap };
        delete newProgressMap[uuid];
        return {
          ...prev,
          isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
          error: isCurrentlyGenerating
            ? i18next.t('j-dingtalk-web_pages_ai-image_UnknownStatusPleaseTryAgain')
            : prev.error,
          // Keep progress at current level for unexpected status, don't reset to 0
          progress: isCurrentlyGenerating ? Math.max(existingProgress, 95) : prev.progress,
          progressMap: newProgressMap,
        };
      });
    } catch (error: any) {
      log('error', error.message);

      // Increment consecutive error count for this image
      const currentErrorCount = (consecutiveErrorsRef.current[uuid] || 0) + 1;
      consecutiveErrorsRef.current[uuid] = currentErrorCount;

      // Only stop polling after 3 consecutive 500 errors
      if (currentErrorCount >= 3) {
        // Handle polling error after 3 consecutive failures
        stopPollingForImage(uuid);
        setState((prev) => {
          const existingProgress = prev.progressMap[uuid] || 0;
          const newProgressMap = { ...prev.progressMap };
          delete newProgressMap[uuid];
          return {
            ...prev,
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            error: isCurrentlyGenerating
              ? i18next.t('j-dingtalk-web_pages_ai-image_FailedToCheckTheImage')
              : prev.error,
            // Keep progress at current level for polling errors, don't reset to 0
            progress: isCurrentlyGenerating ? Math.max(existingProgress, 95) : prev.progress,
            progressMap: newProgressMap,
          };
        });

        if (isCurrentlyGenerating) {
          Toast.fail({
            content: i18next.t('j-dingtalk-web_pages_ai-image_FailedToCheckTheImage'),
            position: 'top',
            maskClickable: true,
            duration: 3,
          });
        }
      } else if (!isUnmountedRef.current) {
        const timer = setTimeout(() => {
          if (!isUnmountedRef.current) {
            pollImageStatus(uuid, pollCount, isCurrentlyGenerating);
          }
        }, pollInterval); // Same interval as normal polling
        pollingTimersRef.current[uuid] = timer;
      }
    }
  };

  // Start polling for a specific image
  const startPollingForImage = (uuid: string, isCurrentlyGenerating = false) => {
    // Don't start polling if already polling this image
    if (pollingTimersRef.current[uuid]) {
      return;
    }
    pollImageStatus(uuid, 0, isCurrentlyGenerating);
  };

  // Check image list and start polling for images that need it
  const checkAndStartPollingForImages = (imageList: ImageInfo[]) => {
    imageList.forEach((image) => {
      if (
        (image.status === 'pending' || image.status === 'processing') &&
        !pollingTimersRef.current[image.uuid]
      ) {
        startPollingForImage(image.uuid);
      }
    });
  };

  // Handle image generation
  const handleGenerateImage = async (isSegImage: boolean) => {
    sendUT('aigc_picture_generate_click', {
      device: isMobileDevice() ? 'mobile' : 'pc',
      imageSize: state.imageSize,
      positivePrompt: state.positivePrompt,
      image: state.uploadedImage,
    });

    if (state.isGenerating) {
      return;
    }

    // Check if user has enough quota (at least 2 times)
    if (quotaUsage && ((quotaUsage.totalQuota || 0) - (quotaUsage.usedQuota || 0)) < 2) {
      // Show insufficient quota message
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_InsufficientQuotaPleaseRecharge'),
        position: 'top',
        maskClickable: true,
        duration: 3,
        onClose: () => {
          const url = getCreateOrderUrl();
          if (isDingTalk()) {
            $openLink({
              url: openDualLink(url),
            });
          } else {
            window.open(url);
          }
        },
      });
      return;
    }

    if (!state.uploadedImage) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_ai-image_PleaseUploadAnImage'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    if (!state.positivePrompt?.trim()) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_ai-image_PleaseEnterACreativeDescription'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    if (!state.imageSize) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_ai-image_PleaseSelectImageSize'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    setState((prev) => ({
      ...prev,
      isGenerating: true,
      error: null,
      progress: 0,
    }));

    try {
      // Call image generation API
      const result = await generateImage({
        imgUrl: state.uploadedImage,
        requirements: state.positivePrompt,
        imgPixel: getImagePixel(state.imageSize),
        isSegImage: isSegImage ? 'y' : 'n',
      }) as any;

      if (result && result.success && result.uuid) {
        sendUT('aigc_picture_generate_success', {
          device: isMobileDevice() ? 'mobile' : 'pc',
          imageSize: state.imageSize,
          positivePrompt: state.positivePrompt,
          image: state.uploadedImage,
        });

        const { uuid } = result;
        const currentTime = new Date().toISOString();
        const newImageInfo: ImageInfo = {
          uuid,
          imgUrls: [],
          originImgUrl: state.uploadedImage,
          prompt: state.positivePrompt,
          imgPixel: getImagePixel(state.imageSize),
          status: 'processing',
          type: 'normal',
          createdAt: currentTime, // Added required createAt field
          requestId: uuid,
          imageSize: state.imageSize,
        };

        // Start with a small initial progress (2%) for new images
        const initialProgress = 2;

        setState((prev) => ({
          ...prev,
          taskId: uuid,
          imageList: [newImageInfo, ...prev.imageList],
          hasImages: true,
          progressMap: {
            ...prev.progressMap,
            [uuid]: initialProgress,
          },
          // Clear form fields after successful generation
          uploadedImage: null,
          positivePrompt: '',
          imageSize: '1:1', // Reset to default size
          // Switch to image list view
          currentStep: isMobileDevice() ? 'imageList' : prev.currentStep,
          rightPanelContent: !isMobileDevice() ? 'imageList' : prev.rightPanelContent,
        }));

        // Scroll to top when switching to image list
        scrollToTop();

        // Start polling for image generation status
        startPollingForImage(uuid, true);

        // Update used quota immediately for UI feedback (increment by 2 for image generation)
        updateUsedQuota(1);
      } else {
        sendUT('aigc_picture_generate_fail', {
          device: isMobileDevice() ? 'mobile' : 'pc',
          imageSize: state.imageSize,
          positivePrompt: state.positivePrompt,
          image: state.uploadedImage,
        });

        throw new Error(
          result?.errorMsg || i18next.t('j-dingtalk-web_pages_ai-image_FailedToGenerate'),
        );
      }
    } catch (error) {
      sendUT('aigc_picture_generate_fail', {
        device: isMobileDevice() ? 'mobile' : 'pc',
        imageSize: state.imageSize,
        positivePrompt: state.positivePrompt,
        image: state.uploadedImage,
      });

      setState((prev) => ({
        ...prev,
        isGenerating: false,
        error:
          error instanceof Error
            ? error.message
            : i18next.t('j-dingtalk-web_pages_ai-image_FailedToGeneratePleaseTry'),
        progress: 0,
      }));

      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_ai-image_ImageGenerationFailedPleaseTry'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Optimized single image update after regeneration
  const handleOptimizedImageUpdate = async (regenerateResult: any, originalUuid: string) => {
    try {
      // If regenerate API returns a new UUID, it means a new image was created
      if (regenerateResult.uuid && regenerateResult.uuid !== originalUuid) {
        // Create new image info object for the regenerated image
        const currentTime = new Date().toISOString();
        const originalImage = state.imageList.find((img) => img.uuid === originalUuid);

        if (originalImage) {
          const newImageInfo: ImageInfo = {
            uuid: regenerateResult.uuid,
            imgUrls: [], // Empty during processing
            originImgUrl: originalImage.originImgUrl,
            prompt: originalImage.prompt,
            imageSize: originalImage.imageSize,
            imgPixel: originalImage.imgPixel || '800x800',
            type: originalImage.type || 'normal',
            status: 'processing',
            requestId: regenerateResult.uuid,
            createdAt: currentTime,
          };

          // Add new image to the front of the list and start polling
          setState((prev) => ({
            ...prev,
            imageList: [newImageInfo, ...prev.imageList],
          }));

          // Start polling for the new image
          startPollingForImage(regenerateResult.uuid);
          return;
        }
      }

      // If no new UUID, check the status of the original image
      const statusResult = await checkImageStatus({ uuid: originalUuid }) as any;

      if (statusResult) {
        // Parse imgUrls if available using utility function
        // Update the specific image in the list
        setState((prev) => ({
          ...prev,
          imageList: prev.imageList.map((img) =>
            (img.uuid === originalUuid
              ? {
                ...img,
                status: statusResult.status || img.status,
                imgUrls: statusResult.imgUrls || img.imgUrls,
                errorMsg: statusResult.errorMsg || img.errorMsg,
                finishTime: formatTimestampToISO(statusResult.finishTime, img.finishTime),
              }
              : img)),
        }));

        // If still processing, start polling
        if (statusResult.status === 'processing' || statusResult.status === 'pending') {
          startPollingForImage(originalUuid);
        }
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to update image status:', error);
      // Fallback to full list reload
      loadImageList();
    }
  };

  // Handle regenerate from list
  const handleRegenerateFromList = (imageInfo: ImageInfo) => {
    // Set form data from the image info
    setState((prev) => ({
      ...prev,
      uploadedImage: imageInfo.originImgUrl || imageInfo.imgUrls?.[0] || '',
      positivePrompt: imageInfo.prompt || '',
      imageSize: getImageRatio(imageInfo.imgPixel || '800x800'),
      currentStep: isMobileDevice() ? 'form' : prev.currentStep,
      rightPanelContent: !isMobileDevice() ? 'welcome' : prev.rightPanelContent,
    }));
  };

  // Render PC layout with left-right structure
  const renderPCLayout = () => {
    // Show loading overlay if images are loading
    if (state.isLoadingImages) {
      return (
        <div className="ai-image-pc-layout">
          <Loading text={i18next.t('j-dingtalk-web_pages_aiVideo_Loading')} />
        </div>
      );
    }

    return (
      <div className="ai-image-pc-layout">
        {/* Left Panel - Always show ImageForm */}
        <div className="ai-image-left-panel">
          <ImageForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateImage}
            quotaUsage={quotaUsage}
          />
        </div>
        {/* Right Panel - Show Welcome or ImageList based on state */}
        <div className="ai-image-right-panel">
          {state.rightPanelContent === 'welcome' && (
            <WelcomeScreen
              quotaUsage={quotaUsage}
              hasGenerateBtn={false}
              onOrderClick={handleOrderListClick}
            />
          )}
          {state.rightPanelContent === 'imageList' && (
            <ImageList
              imageList={state.imageList}
              isLoading={false} // Loading is handled at page level
              error={state.imageListError}
              onCreateNew={handleCreateNewImage}
              onRegenerate={handleRegenerateFromList}
              onRefresh={handleRefreshImageList}
              progressMap={state.progressMap}
              hasNextPage={state.hasNextPage}
              isLoadingMore={state.isLoadingMore}
              onLoadMore={loadMoreImages}
              loadImageList={loadImageList}
              onOptimizedImageUpdate={handleOptimizedImageUpdate}
            />
          )}
        </div>
      </div>
    );
  };

  // Render mobile layout
  const renderMobileLayout = () => {
    // Show loading screen if images are loading
    if (state.isLoadingImages) {
      return <Loading text={i18next.t('j-dingtalk-web_pages_aiVideo_Loading')} />;
    }

    switch (state.currentStep) {
      case 'welcome':
        return (
          <WelcomeScreen
            hasGenerateBtn
            quotaUsage={quotaUsage}
            onGetStarted={(e) => handleStepChange('form', e)}
          />
        );

      case 'form':
        return (
          <ImageForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateImage}
            quotaUsage={quotaUsage}
          />
        );

      case 'imageList':
        return (
          <ImageList
            imageList={state.imageList}
            isLoading={false} // Loading is handled at page level
            error={state.imageListError}
            onCreateNew={handleCreateNewImage}
            onRegenerate={handleRegenerateFromList}
            onRefresh={handleRefreshImageList}
            progressMap={state.progressMap}
            hasNextPage={state.hasNextPage}
            isLoadingMore={state.isLoadingMore}
            onLoadMore={loadMoreImages}
            loadImageList={loadImageList}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="ai-image-page">
      {/* Mobile Navigation Bar */}
      <MobileNavbar {...navbarConfig} />

      {isMobileDevice() ? renderMobileLayout() : renderPCLayout()}

      {/* Free Modal */}
      <FreeModal
        visible={freeModalVisible}
        quotaAccessData={quotaAccessData}
        onClose={handleFreeModalClose}
        onReceive={handleFreeModalReceive}
        loading={freeModalLoading}
      />
    </div>
  );
};

export default AIImagePage;
