import React, { useEffect, useState } from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { setPageTitle, openLink, isPc } from '@/utils/jsapi';
import { CheckCircleOutlined } from '@ali/ding-icons';
import { Toast } from 'dingtalk-design-mobile';
import theme, { IThemeType } from 'dingtalk-theme';
import MobileNavbar from '@/components/MobileNavbar';
import { createOrder, CreateResourceOrderRequest } from '@/apis/quota';
import useQuotaUsage from '@/hooks/useQuotaUsage';
import QuotaDisplay from '@/components/QuotaDisplay';
import { getOrderListUrl } from '@/utils/env';
import './index.less';

theme.setTheme(IThemeType.dark);

// Product data interface
interface ProductInfo {
  credits: number;
  videoCredits: number;
  discountPrice: number;
  taxPrice: number;
  totalPrice: number;
  currency: string;
}

// Mock product data based on the design
const PRODUCT_DATA: ProductInfo = {
  credits: 200,
  videoCredits: 100,
  discountPrice: 2000,
  taxPrice: 200,
  totalPrice: 2200,
  currency: '円',
};

// Fixed item code for this product
const ITEM_CODE = 'DT_GOODS_MATERIAL_101001';

const CreateOrder: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);

  // 使用 useQuotaUsage hook 管理额度数据
  const { quotaUsage, refreshQuotaUsage } = useQuotaUsage('ai_material');

  useEffect(() => {
    // Set page title for mobile
    setPageTitle(i18next.t('j-dingtalk-web_pages_create-order_Title'));
  }, []);

  // Handle payment button click
  const handlePaymentClick = async () => {
    setIsLoading(true);

    try {
      // Prepare order request data
      const orderRequest: CreateResourceOrderRequest = {
        itemCode: ITEM_CODE,
        resourceKey: 'ai_material',
      };

      // Create order
      const response = await createOrder(orderRequest) as any;

      if (response.success) {
        const { orderUuid: newOrderUuid } = response.data;
        // Handle different scenarios based on quotaGranted
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_create-order_TheOrderHasBeenGenerated'),
          position: 'top',
          duration: 3,
          maskClickable: true,
          onClose: () => {
            setIsLoading(false);
            // Refresh quota usage after successful order
            refreshQuotaUsage();
            // Jump to order detail page with orderUuid parameter
            const orderDetailUrl = `${getOrderListUrl()}&orderUuid=${newOrderUuid}`;

            // Use DingTalk JSAPI for page navigation in PC environment
            if (isPc) {
              openLink(orderDetailUrl);
            } else {
              // Fallback to window.location.href for mobile or browser
              window.location.href = orderDetailUrl;
            }
          },
        });
      } else {
        setIsLoading(false);
        // Handle order creation failure
        Toast.fail({
          content:
            response.errorMsg ||
            i18next.t('j-dingtalk-web_pages_create-order_OrderGenerationFailedPleaseTry'),
          position: 'top',
          duration: 3,
          maskClickable: true,
          icon: 'error',
        });
      }
    } catch (error) {
      setIsLoading(false);
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_create-order_OrderGenerationFailedPleaseTry'),
        position: 'top',
        duration: 3,
        maskClickable: true,
      });
    }
  };

  // Feature list data
  const features = [
    {
      key: 'feature1',
      text: i18next.t('j-dingtalk-web_pages_create-order_Feature1'),
    },
    {
      key: 'feature2',
      text: i18next.t('j-dingtalk-web_pages_create-order_Feature2'),
    },
    {
      key: 'feature3',
      text: i18next.t('j-dingtalk-web_pages_create-order_Feature3'),
    },
  ];

  // Format price with currency
  const formatPrice = (price: number): string => {
    return `${price}${PRODUCT_DATA.currency}`;
  };

  // Generate description text with interpolation
  const getDescriptionText = (): string => {
    return i18next.t('j-dingtalk-web_pages_create-order_Description', {
      videoCount: PRODUCT_DATA.videoCredits,
    });
  };

  return (
    <div className="create-order-page">
      {/* Mobile Navigation */}
      <MobileNavbar
        title={i18next.t('j-dingtalk-web_pages_create-order_Title')}
        showShareButton={false}
        showOrderButton={false}
      />

      <div className="content-wrapper">
        {/* Header Section */}
        <div className="header-section">
          <h1 className="page-title">{i18next.t('j-dingtalk-web_pages_create-order_Title')}</h1>
          <div className="usage-count">
            <span className="usage-label">
              {i18next.t('j-dingtalk-web_pages_create-order_UsageCount')}：
              <QuotaDisplay quotaUsage={quotaUsage} placeholder="0/0" />
            </span>
          </div>
        </div>

        {/* Main Product Card */}
        <div className="main-card">
          <div className="card-content">
            {/* Credits Display */}
            <div className="credits-number">
              {PRODUCT_DATA.credits}
              <span className="credits-unit">
                {i18next.t('j-dingtalk-web_pages_create-order_Credits')}
              </span>
            </div>

            {/* Description */}
            <p className="credits-description">
              {PRODUCT_DATA.credits}
              {getDescriptionText()}
            </p>

            <div className="feature-divider" />

            {/* Features List */}
            <ul className="features-list">
              {features.map((feature) => (
                <li key={feature.key} className="feature-item">
                  <CheckCircleOutlined className="feature-icon" />
                  <p className="feature-text">{feature.text}</p>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div className="footer-section">
        {/* Price Section */}
        <div className="price-section">
          <div className="price-row">
            <span className="price-label">
              {i18next.t('j-dingtalk-web_pages_create-order_ProductPrice')}
            </span>
            <div className="price-value">
              <span className="discount-tag">
                {i18next.t('j-dingtalk-web_pages_create-order_DiscountPrice')}
              </span>
              <span className="discount-price">{formatPrice(PRODUCT_DATA.discountPrice)}</span>
            </div>
          </div>
          <div className="price-row">
            <span className="price-label">
              {i18next.t('j-dingtalk-web_pages_create-order_TaxPrice')}
            </span>
            <div className="price-value">
              <span className="discount-price">{formatPrice(PRODUCT_DATA.taxPrice)}</span>
            </div>
          </div>
          <div className="price-row">
            <span className="price-label">
              {i18next.t('j-dingtalk-web_pages_create-order_TotalPrice')}
            </span>
            <div className="price-value">
              <span className="discount-price">{formatPrice(PRODUCT_DATA.totalPrice)}</span>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <button className="action-button" onClick={handlePaymentClick} disabled={isLoading}>
          {isLoading
            ? i18next.t('j-dingtalk-web_pages_create-order_LiZhong')
            : // Processing...
            i18next.t('j-dingtalk-web_pages_create-order_PaymentButton')}
        </button>
      </div>
    </div>
  );
};

export default CreateOrder;
