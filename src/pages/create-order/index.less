.create-order-page {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background:
    radial-gradient(ellipse 600px 500px at top right,
      transparent 0%,
      transparent 30%,
      rgba(20, 20, 20, 0.1) 35%,
      rgba(20, 20, 20, 0.3) 45%,
      rgba(20, 20, 20, 0.5) 55%,
      rgba(20, 20, 20, 0.7) 65%,
      rgba(20, 20, 20, 0.85) 75%,
      #141414 85%
    ),
    linear-gradient(180deg, #6B1538 10%, #2F1624 100%);
  backdrop-filter: blur(40px);

  // 添加模糊过渡效果的伪元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 70%;
    height: 60%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.15) 0%,
      rgba(107, 21, 56, 0.1) 20%,
      rgba(47, 22, 36, 0.08) 40%,
      rgba(47, 22, 36, 0.05) 60%,
      transparent 80%
    );
    filter: blur(50px);
    pointer-events: none;
    z-index: 1;
  }

  // 添加第二层更柔和的模糊效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 80%;
    height: 70%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.08) 0%,
      rgba(47, 22, 36, 0.05) 30%,
      rgba(20, 20, 20, 0.03) 50%,
      transparent 70%
    );
    filter: blur(80px);
    pointer-events: none;
    z-index: 0;
  }

  // Main content wrapper
  .content-wrapper {
    padding: calc(44px + env(safe-area-inset-top)) 16px 20px 16px; // 44px nav + 20px top padding + 20px extra for safe area
    position: relative;
    min-height: calc(100vh - 52px); // Ensure minimum height for mobile
    max-width: 100%;
    margin: 0 auto;

    // Header section
    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      position: relative;

      .page-title {
        font-size: 24px;
        line-height: 34px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      .usage-count {
        display: flex;
        align-items: center;
        gap: 4px;
        flex-shrink: 0;

        .usage-label {
          font-size: 12px;
          line-height: 18px;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
          margin: 0;
          white-space: nowrap;

          .total-quota {
            color: rgba(255, 255, 255, 0.6);
            margin-left: 4px;
          }
        }
      }
    }

    // Main card section
    .main-card {
      background: url('https://img.alicdn.com/imgextra/i2/O1CN01aeQWyl1R54aRznMz1_!!6000000002059-2-tps-1029-768.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      backdrop-filter: blur(70px);
      border-radius: 24px;
      padding: 24px 16px;
      position: relative;
      overflow: hidden;

      .card-content {
        position: relative;
        text-align: center;

        .credits-number {
          font-size: 40px;
          font-weight: 600;
          line-height: 24px;
          color: #ffffff;
          margin: 0 0 8px 0;

          .credits-unit {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin-left: 4px;
          }
        }

        .credits-description {
          font-size: 14px;
          line-height: 20px;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
          margin: 0 0 24px 0;
        }

        .feature-divider {
          width: 100%;
          opacity: 0.5;
          border-top: 0.5px solid #ffffff;
        }

        .features-list {
          list-style: none;
          padding: 0;
          margin: 16px 0 0 0;

          .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }

            .feature-icon {
              width: 16px;
              height: 16px;
              font-size: 16px;
              color: #ffffff;
            }

            .feature-text {
              font-size: 14px;
              line-height: 18px;
              font-weight: 500;
              color: rgba(255, 255, 255, 0.9);
              margin: 0;
            }
          }
        }
      }
    }
  }

  .footer-section {
    position: fixed;
    bottom: calc(24px + env(safe-area-inset-bottom));
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    margin: 0 16px;
    gap: 24px;

    // Price section
    .price-section {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .price-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;

        .price-label {
          font-size: 14px;
          line-height: 20px;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
          margin: 0;
        }

        .price-value {
          display: flex;
          align-items: center;
          gap: 8px;

          .discount-tag {
            font-size: 10px;
            line-height: 14px;
            color: #FF0E53;
            padding: 0 4px;
            border-radius: 4px;
            border: 0.5px solid #FF0E53;
          }

          .discount-price {
            font-size: 20px;
            line-height: 28px;
            font-weight: 500;
            color: #FF0E53;
          }
        }
      }
    }

    // Action button
    .action-button {
      width: 100%;
      background: #FF0E53;
      border: none;
      border-radius: 40px;
      padding: 8px 24px;
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
      color: #ffffff;
      cursor: pointer;
      transition: all 0.24s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;

      &:hover {
        background: #e60026;
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      &:active {
        background: #cc001f;
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }
    }
  }

  // PC 端样式 (屏幕宽度大于 768px)
  @media (min-width: 768px) {
    .content-wrapper {
      min-height: auto;
      max-width: 360px;
      width: 360px;
      margin-bottom: 120px;
      padding: 24px 16px 20px 16px;
      position: relative;
      z-index: 2;

      .header-section {
        margin-top: 0;
        margin-bottom: 32px;

        .page-title {
          font-size: 28px;
          line-height: 38px;
        }

        .usage-count {
          .usage-label {
            font-size: 14px;
            line-height: 20px;
          }
        }
      }
    }

    .footer-section {
      position: relative;
      bottom: auto;
      left: auto;
      right: auto;
      margin: 0 auto;
      max-width: 360px;
      width: 360px;
      padding: 0 16px 24px 16px;
    }
  }

  // Mobile 端样式 (屏幕宽度小于等于 768px)
  @media (max-width: 768px) {
    .content-wrapper {
      padding: calc(44px + env(safe-area-inset-top)) 16px 20px 16px;

      .header-section {
        flex-direction: row;
        align-items: center;
        gap: 12px;
        margin-top: 24px;
        margin-bottom: 16px;

        .page-title {
          font-size: 20px;
          line-height: 28px;
          color: rgba(255, 255, 255, 0.9) !important;
        }
      }

      .action-button {
        padding: 18px 24px;
        font-size: 16px;
      }
    }
  }
}

@media (min-width: 768px) {
  .create-order-page {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
}