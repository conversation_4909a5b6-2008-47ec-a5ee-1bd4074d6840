@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.store-list {
  padding: 8px;
}

.store-card {
  display: flex;
  align-items: center;
  background: @common_bg_z1_color;
  border-radius: @common_border_radius_l;
  padding: 8px;
}

.store-logo {
  width: 100px;
  height: 100px;
  border-radius: @common_border_radius_m;
  object-fit: contain;
  margin-right: 16px;
  align-self: flex-start;
}

.store-info {
  flex: 1;
  display: flex;
  padding: 8px 0;
  flex-direction: column;
  justify-content: center;
}

.store-meta-container {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  flex-direction: column;
  .store-meta {
    .common_description_text_style_mob();
    color: @common_level2_base_color;
    display: flex;
    gap: 4px;
  }
}

.store-name {
  .common_body_text_style();
  color: @common_level1_base_color;
}

.store-like {
  font-size: 24px;
  color: @theme_primary1_color;
  margin-left: 8px;
  margin-right: 8px;
  align-self: flex-end;
  .store-like-icon {
    width: 13px;
  }
}
