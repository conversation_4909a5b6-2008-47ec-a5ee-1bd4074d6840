import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { InfiniteList } from '@/components/InfiniteList';
import { useStoreFavorites } from '../services';
import OptimizedImage from '@/components/OptimizedImage';
import { openLink$ } from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { HeartOutlined } from '@ali/ding-icons';
import './StoreView.less';

const StoreView: React.FC = () => {
  const {
    data,
    loading,
    error,
    loadMore,
    noMore,
  } = useStoreFavorites();

  const stores = data?.list || [];

  // 渲染单个店铺卡片
  const renderStoreItem = (store: any) =>
    (<div
      className="store-card"
      key={store.creatorId}
      onClick={() => {
        openLink$({
          url: `dingtalk://dingtalkclient/page/j_create_detail?creatorId=${encodeURIComponent(store.creatorId)}`,
          enableShare: false,
        });
      }}
    >
      <OptimizedImage
        className="store-logo"
        src={store.avatar}
        alt={store.name}
        width={40}
        height={40}
      />
      <div className="store-info">
        <div className="store-name">{store.name}</div>
        <div className="store-meta-container">
          <div className="store-meta">
            <span>{i18next.t('j-dingtalk-web_pages_my-favorites_components_StoreView_Contribution')}:</span>
            <span>{store.feedCount || 0}</span>
          </div>
          <div className="store-meta">
            <span>{i18next.t('j-dingtalk-web_pages_premiumStore_NumberOfFans')}: </span>
            <span>{store.followerCount || 0}</span>
          </div>
        </div>
      </div>
      <div className="store-like">
        <img className="store-like-icon" src="https://img.alicdn.com/imgextra/i1/O1CN011cxQ6i1akb233RZrN_!!6000000003368-2-tps-41-39.png" />
      </div>
    </div>
    );


  return (
    <InfiniteList
      data={stores}
      renderItem={renderStoreItem}
      loading={loading}
      error={!!error}
      hasMore={!noMore}
      layout="list"
      onLoadMore={loadMore}
      emptyIcon={<HeartOutlined />}
      emptyText={i18next.t('j-dingtalk-web_pages_my-favorites_components_StoreView_NoStoreForCollection')}
      errorText={i18next.t('j-dingtalk-web_pages_my-favorites_components_StoreView_NetworkErrorPleaseTryAgain')}
    />);
};

export default StoreView;
