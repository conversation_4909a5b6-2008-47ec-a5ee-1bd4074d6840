import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { RecFeedDetail } from '@/apis/feed-action';
import { useCollectedFeedList, generateProductUrl } from '../services';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { InfiniteList } from '@/components/InfiniteList';
import ProductCard from '../../my-like/components/ProductCard';
import { HeartOutlined } from '@ali/ding-icons';

const TrendingView: React.FC = () => {
  const {
    data,
    loading,
    error,
    loadMore,
    noMore,
  } = useCollectedFeedList();

  const products = data?.list || [];

  // 点击跳转
  const handleProductClick = (product: RecFeedDetail) => {
    const url = generateProductUrl(product);
    $openLink({ url });
  };

  return (
    <InfiniteList
      data={products}
      renderItem={(item) =>
        <ProductCard key={item.itemId} product={item} onProductClick={handleProductClick} />
      }
      loading={loading}
      error={!!error}
      hasMore={!noMore}
      layout="waterfall"
      onLoadMore={loadMore}
      emptyIcon={<HeartOutlined />}
      emptyText={i18next.t('j-dingtalk-web_pages_my-favorites_components_TrendingView_NoFavoriteProducts')}
      errorText={i18next.t('j-dingtalk-web_pages_my-favorites_components_TrendingView_NetworkErrorPleaseTryAgain')}
    />);
};

export default TrendingView;
