import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { RecUserCommentModel } from '@/apis/feed-action';
import { useMyCommentList, generateProductUrl } from './services';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { InfiniteList } from '@/components/InfiniteList';
import CommentCard from './components/CommentCard';
import { useTitle } from '@/hooks/useTitle';
import { InformationOutlined } from '@ali/ding-icons';
import './index.less';

const MyCommentPage: React.FC = () => {
  const {
    data,
    loading,
    loadingMore,
    error,
    loadMore,
    noMore,
  } = useMyCommentList();

  const comments = data?.list || [];

  useTitle(i18next.t('j-dingtalk-web_pages_my-comment_MyComments'));

  // 点击跳转
  const handleCommentClick = (comment: RecUserCommentModel) => {
    const url = generateProductUrl(comment);
    $openLink({ url });
  };

  return (
    <InfiniteList
      data={comments}
      renderItem={(item) =>
        <CommentCard key={item.commentId} data={item} onCommentClick={handleCommentClick} />
      }
      loading={loading || loadingMore}
      error={!!error}
      hasMore={!noMore}
      layout="list"
      emptyIcon={<InformationOutlined />}
      onLoadMore={loadMore}
      emptyText={i18next.t('j-dingtalk-web_pages_my-comment_NoCommentRecords')}
      errorText={i18next.t('j-dingtalk-web_pages_my-comment_NetworkErrorPleaseTryAgain')}
    />
  );
};

export default MyCommentPage;
