import React from 'react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { RecUserCommentModel } from '@/apis/feed-action';
import { parseCommentContent } from '@/utils/commentUtils';
import { i18next } from '@ali/dingtalk-i18n';
import OptimizedImage from '@/components/OptimizedImage';
import './CommentCard.less';

// 配置 dayjs 插件
dayjs.extend(relativeTime);

interface CommentCardProps {
  data: RecUserCommentModel;
  onCommentClick?: (comment: RecUserCommentModel) => void;
}

const CommentCard: React.FC<CommentCardProps> = ({ data, onCommentClick }) => {
  const handleClick = () => {
    onCommentClick?.(data);
  };

  // 格式化时间戳为相对时间
  const formatTime = (timestamp: number): string => {
    const now = dayjs();
    const commentTime = dayjs(timestamp);
    const diffDays = now.diff(commentTime, 'day');

    if (diffDays === 0) return i18next.t('j-dingtalk-web_pages_my-comment_components_CommentCard_Today');
    if (diffDays === 1) return i18next.t('j-dingtalk-web_pages_my-comment_components_CommentCard_Yesterday');
    if (diffDays < 7) return i18next.t('j-dingtalk-web_pages_my-comment_components_CommentCard_DiffdaysDaysAgo', { diffDays });

    // 7天以后直接显示日期
    return commentTime.format(i18next.t('j-dingtalk-web_pages_my-comment_components_CommentCard_MMonthDDay'));
  };

  return (
    <div className="my-comment-card" onClick={handleClick}>
      <OptimizedImage
        className="my-comment-card-img"
        src={data.imageUrl}
        width={100}
        height={100}
      />
      <div className="my-comment-card-content">
        <div className="my-comment-card-title">{data.productTitle}</div>
        <div className="my-comment-card-date">{formatTime(data.commentTimestamp)}</div>
        <div className="my-comment-card-comment">{parseCommentContent(data.comment.content)}</div>
      </div>
    </div>);
};

export default CommentCard;
