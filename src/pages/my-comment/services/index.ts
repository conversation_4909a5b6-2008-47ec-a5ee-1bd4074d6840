import { useInfiniteScroll } from 'ahooks';
import {
  selectCommentByUid,
  RecFeedUserCommentListRequestModel,
  RecFeedUserCommentListResultModel,
  RecUserCommentModel,
} from '@/apis/feed-action';

// 获取我的评论列表
export const getMyCommentList = async (params: {
  cursor?: string;
  size?: number;
}): Promise<{
  data: RecUserCommentModel[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: RecFeedUserCommentListRequestModel = {
    cursor: params.cursor,
    size: params.size || 20,
  };

  const response: RecFeedUserCommentListResultModel = await selectCommentByUid(requestParams);

  return {
    data: response.commentInfoList || [],
    cursor: response.cursor,
    hasMore: response.hasMore,
  };
};

// 使用 useInfiniteScroll 封装请求，支持无限滚动
export const useMyCommentList = () => {
  return useInfiniteScroll(
    async (params: { list?: RecUserCommentModel[]; cursor?: string; size?: number } = {}) => {
      const result = await getMyCommentList(params);
      return {
        list: result.data,
        cursor: result.hasMore,
        hasMore: result.hasMore,
      };
    },
    {
      isNoMore: (res) => !res?.hasMore,
    },
  );
};

// 生成产品详情链接
export const generateProductUrl = (comment: RecUserCommentModel): string => {
  // 使用商品跳转链接
  return `dingtalk://dingtalkclient/page/j_feed_detail?feedId=${comment.feedId}&commentId=${comment.commentId}`;
};
