.welcome-screen {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 106px);
  padding: 16px;
  // Add iOS safe area support for bottom padding
  padding-bottom: calc(env(safe-area-inset-bottom) + 16px);

  .welcome-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    max-width: 400px;
    width: 100%;

    .logo-section {
      text-align: center;
      margin-bottom: 72px;

      .logo-image {
        display: block;
        width: 64px;
        height: 64px;
        color: white;
        margin: 0 auto 16px;
      }

      .title {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 12px;
        color: rgba(255, 255, 255, 0.9);
      }

      .subtitle {
        font-size: 14px;
        line-height: 24px;
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .get-started-btn {
      width: 150px;
      height: 56px;
      line-height: 56px;
      border-radius: 28px;
      font-size: 24px;
      font-weight: 500;
      text-align: center;
      border: none;
      background: white;
      color: #141414;

      &:hover {
        background: rgba(255, 255, 255, 0.9);
        color: #141414;
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// PC Layout specific styles
.ai-video-pc-layout .ai-video-right-panel .welcome-screen {
  padding: 40px 32px;

  .welcome-content {
    max-width: 500px;

    .logo-section {
      margin-bottom: 48px;

      .logo-image {
        width: 64px;
        height: 64px;
        color: white;
        margin-bottom: 24px;
      }

      .title {
        font-size: 32px;
        margin-bottom: 16px;
        color: rgba(255, 255, 255, 0.9);
      }

      .subtitle {
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}
