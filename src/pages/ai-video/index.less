.ai-video-page {
  min-height: 100vh;
  overflow: hidden; // Fixed typo: hidde -> hidden
  background:
    radial-gradient(ellipse 600px 500px at top right,
      transparent 0%,
      transparent 30%,
      rgba(20, 20, 20, 0.1) 35%,
      rgba(20, 20, 20, 0.3) 45%,
      rgba(20, 20, 20, 0.5) 55%,
      rgba(20, 20, 20, 0.7) 65%,
      rgba(20, 20, 20, 0.85) 75%,
      #141414 85%
    ),
    linear-gradient(180deg, #6B1538 10%, #2F1624 100%);
  backdrop-filter: blur(40px);

  // 添加模糊过渡效果的伪元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 70%;
    height: 60%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.15) 0%,
      rgba(107, 21, 56, 0.1) 20%,
      rgba(47, 22, 36, 0.08) 40%,
      rgba(47, 22, 36, 0.05) 60%,
      transparent 80%
    );
    filter: blur(50px);
    pointer-events: none;
    z-index: 1;
  }

  // 添加第二层更柔和的模糊效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 80%;
    height: 70%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.08) 0%,
      rgba(47, 22, 36, 0.05) 30%,
      rgba(20, 20, 20, 0.03) 50%,
      transparent 70%
    );
    filter: blur(80px);
    pointer-events: none;
    z-index: 0;
  }
}

// PC Layout Styles
.ai-video-pc-layout {
  display: flex;
  height: 100vh; // Fixed height for the entire layout
  margin: 0 auto;
  // background-color: #000000;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

  .ai-video-left-panel {
    flex: 0 0 412px; // Fixed width for form panel
    height: 100vh; // Fixed height
    border-right: none;
    background-color: #1d1d1d;
    overflow-y: auto; // Enable vertical scrolling for left panel content
    overflow-x: hidden; // Hide horizontal scrollbar

    // Remove min-height constraint to allow proper scrolling
    > div {
      height: auto; // Let content determine its natural height
    }
  }

  .ai-video-right-panel {
    flex: 1; // Take remaining space
    height: 100vh; // Fixed height
    // background-color: var(--common_bg_color); // Match WelcomeScreen background to avoid white line at bottom
    overflow-y: auto; // Enable vertical scrolling for right panel content
    overflow-x: hidden; // Hide horizontal scrollbar
    position: relative;

    // Remove min-height constraint to allow proper scrolling
    > div {
      height: auto; // Let content determine its natural height
    }

    .video-list {
      max-width: 560px;
    }

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// Responsive design - on smaller screens, fall back to mobile layout
@media (max-width: 768px) {
  .ai-video-page {
    overflow: auto; // Allow normal scrolling on mobile
    padding-top: calc(44px + env(safe-area-inset-top)); // Add space for mobile navbar
  }

  .ai-video-pc-layout {
    flex-direction: column;
    height: auto; // Remove fixed height on mobile
    max-width: none;
    box-shadow: none;

    .ai-video-left-panel {
      flex: none;
      height: auto; // Remove fixed height on mobile
      border: none;
      overflow-y: visible; // Remove scroll on mobile

      > div {
        height: auto; // Natural height on mobile
      }
    }

    .ai-video-right-panel {
      height: auto; // Remove fixed height on mobile
      overflow-y: visible; // Remove scroll on mobile

      > div {
        height: auto; // Natural height on mobile
      }
    }
  }
}
