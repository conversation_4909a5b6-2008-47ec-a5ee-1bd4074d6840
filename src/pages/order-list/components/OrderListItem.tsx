import React from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { formatCompletionTime } from '@/utils/util';
import { OrderListItemProps, OrderStatus } from '../types';

// Get status display info
const getStatusInfo = (status: OrderStatus) => {
  const statusMap = {
    new: {
      text: i18next.t('j-dingtalk-web_pages_order-list_PendingPayment'),
    },
    running: {
      text: i18next.t('j-dingtalk-web_pages_order-list_UnderReview'),
    },
    agree: {
      text: i18next.t('j-dingtalk-web_pages_order-list_ReviewPassed'),
    },
    disagree: {
      text: i18next.t('j-dingtalk-web_pages_order-list_ReviewFailed'),
    },
    cancel: {
      text: i18next.t('j-dingtalk-web_pages_order-list_OrderCanceled'),
    },
  };
  return statusMap[status];
};

const OrderListItem:
React.FC<OrderListItemProps> = ({ orderInfo, onClick, isSelected = false }) => {
  const statusInfo = getStatusInfo(orderInfo.status);

  const handleClick = () => {
    onClick(orderInfo);
  };

  return (
    <div
      className={`order-list-item order-status-${orderInfo.status} ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
    >
      <div className="order-header">
        <div className="order-number">
          <span className="label">{i18next.t('j-dingtalk-web_pages_order-list_OrderNumber')}:</span>
          <span className="value">{orderInfo.orderUuid}</span>
        </div>
        <div className="order-time">
          <span className="label">{i18next.t('j-dingtalk-web_pages_order-list_OrderTime')}:</span>
          <span className="value">{formatCompletionTime(new Date(orderInfo.gmtCreate))}</span>
        </div>
      </div>

      <div className="order-footer">
        <div className="order-status">
          {statusInfo.text}
        </div>
        <div className="product-price">
          <span className="label">{i18next.t('j-dingtalk-web_pages_order-list_ProductPrice')}:</span>
          <span className="value">2200円</span>
        </div>
      </div>
    </div>
  );
};

export default OrderListItem;
