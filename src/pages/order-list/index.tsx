import React, { useState, useEffect } from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { OrderOutlined } from '@ali/ding-icons';
import { setPageTitle } from '@/utils/jsapi';
import { getUrlParam } from '@/utils/util';
import { getOrderList, getOrderDetail } from '@/apis/quota';
import theme, { IThemeType } from 'dingtalk-theme';
import MobileNavbar from '@/components/MobileNavbar';
import Loading from '@/components/Loading';
import { InfiniteList } from '@/components/InfiniteList';
import OrderListItem from './components/OrderListItem';
import OrderDetail from './components/OrderDetail';
import {
  OrderInfo,
  PageResourceOrderRequest,
  PageResourceOrderResponse,
  ResourceOrderDetailResponse,
  OrderStatus,
} from './types';
import './index.less';

theme.setTheme(IThemeType.dark);

const OrderListPage: React.FC = () => {
  const [currentView, setCurrentView] = useState<'list' | 'detail'>('list');
  const [selectedOrder, setSelectedOrder] = useState<OrderInfo | null>(null);
  const [orderList, setOrderList] = useState<OrderInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [hasMore, setHasMore] = useState(true);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
  });

  useEffect(() => {
    // Set page title
    setPageTitle(i18next.t('j-dingtalk-web_pages_order-list_MyOrders'));

    // Check url param for orderUuid
    const orderUuidFromUrl = getUrlParam('orderUuid');

    // Check if we have a saved view state in sessionStorage
    const savedView = sessionStorage.getItem('orderListCurrentView');
    const savedOrderUuid = sessionStorage.getItem('orderListSelectedOrderUuid');

    if (orderUuidFromUrl) {
      // URL parameter takes priority
      // For PC layout, we need both list and detail data
      // Don't auto-select first order since we'll load specific order detail
      loadOrderList(1, false).then(() => {
        loadOrderDetail(orderUuidFromUrl);
      });
    } else if (savedView === 'detail' && savedOrderUuid) {
      // Restore detail view with saved order
      // For PC layout, we need both list and detail data
      // Don't auto-select first order since we'll load specific order detail
      loadOrderList(1, false).then(() => {
        loadOrderDetail(savedOrderUuid);
      });
    } else {
      // Default to list view
      loadOrderList();
    }
  }, []);

  // Save current view state to sessionStorage
  const saveViewState = (view: 'list' | 'detail', orderUuid?: string) => {
    sessionStorage.setItem('orderListCurrentView', view);
    if (orderUuid) {
      sessionStorage.setItem('orderListSelectedOrderUuid', orderUuid);
    } else {
      sessionStorage.removeItem('orderListSelectedOrderUuid');
    }
  };

  // Load order list from API
  const loadOrderList = async (page = 1, autoSelectFirst = true) => {
    setLoading(true);
    setError('');

    try {
      const request: PageResourceOrderRequest = {
        resourceKey: 'ai_material',
        currentPage: page,
        pageSize: pagination.pageSize,
      };

      const response = (await getOrderList(request)) as PageResourceOrderResponse;

      if (response.success) {
        const convertedOrders = response.data.map((item) => ({
          orderUuid: item.orderUuid,
          orderData: item.orderData,
          status: item.status as OrderStatus,
          orderQuota: item.orderQuota,
          gmtCreate: (item.gmtCreate ?? new Date(item.gmtCreate)) as Date,
          gmtModified: (item.gmtModified ?? new Date(item.gmtModified)) as Date,
        }));

        // 如果是第一页，替换数据；如果是后续页，追加数据
        if (page === 1) {
          setOrderList(convertedOrders as OrderInfo[]);
          // 设置第一个订单为默认选中（仅在autoSelectFirst为true且没有选中订单时）
          if (autoSelectFirst && convertedOrders.length > 0 && !selectedOrder) {
            setSelectedOrder(convertedOrders[0] as OrderInfo);
          }
        } else {
          setOrderList((prev) => [...prev, ...convertedOrders] as OrderInfo[]);
        }

        setPagination({
          currentPage: response.currentPage,
          pageSize: response.pageSize,
          totalCount: response.totalCount,
        });

        // 设置是否还有更多数据
        setHasMore(response.currentPage * response.pageSize < response.totalCount);
      } else {
        setError(
          response.errorMsg || i18next.t('j-dingtalk-web_pages_order-list_FailedToGetTheOrder'),
        );
      }
    } catch (err) {
      setError(i18next.t('j-dingtalk-web_pages_order-list_NetworkErrorPleaseTryAgain'));
    } finally {
      setLoading(false);
    }
  };

  // Handle order item click
  const handleOrderClick = (orderInfo: OrderInfo) => {
    setSelectedOrder(orderInfo);
    setCurrentView('detail');
    saveViewState('detail', orderInfo.orderUuid);
  };

  // Load order detail by orderUuid
  const loadOrderDetail = async (orderUuid: string) => {
    setLoading(true);
    setError('');

    try {
      const response = (await getOrderDetail({ orderUuid })) as ResourceOrderDetailResponse;

      if (response.success && response.data) {
        const { data } = response;
        const orderInfo: OrderInfo = {
          orderUuid: data.orderUuid,
          orderData: data.orderData,
          status: data.status as OrderStatus,
          orderQuota: data.orderQuota,
          gmtCreate: (data.gmtCreate ?? new Date(data.gmtCreate)) as Date,
          gmtModified: (data.gmtModified ?? new Date(data.gmtModified)) as Date,
        };
        setSelectedOrder(orderInfo);
        setCurrentView('detail');
        saveViewState('detail', orderUuid);
      } else {
        setError(
          response.errorMsg ||
            i18next.t('j-dingtalk-web_pages_order-list_FailedToObtainOrderDetails'),
        );
      }
    } catch (err) {
      setError(i18next.t('j-dingtalk-web_pages_order-list_NetworkErrorPleaseTryAgain'));
    } finally {
      setLoading(false);
    }
  };

  // Handle back from detail page
  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedOrder(null);
    saveViewState('list');
  };

  // Handle refresh
  const handleRefresh = () => {
    setHasMore(true);
    setPagination((prev) => ({ ...prev, currentPage: 1 }));

    // Refresh based on current view
    if (currentView === 'detail' && selectedOrder) {
      loadOrderDetail(selectedOrder.orderUuid);
    } else {
      loadOrderList(1);
    }
  };

  // Handle load more for infinite scroll
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      loadOrderList(pagination.currentPage + 1);
    }
  };

  // Render order list content based on state
  const renderOrderListContent = (isPCMode = false) => {
    if (loading && orderList.length === 0) {
      return <Loading />;
    }

    if (error) {
      return (
        <div className={isPCMode ? 'pc-error-container' : 'error-container'}>
          <p className="error-message">{error}</p>
          <button className="retry-button" onClick={handleRefresh}>
            {i18next.t('j-dingtalk-web_pages_order-list_Retry')}
          </button>
        </div>
      );
    }

    return (
      <InfiniteList
        data={orderList}
        renderItem={(order) => (
          <OrderListItem
            key={order.orderUuid}
            orderInfo={order}
            onClick={handleOrderClick}
            isSelected={selectedOrder?.orderUuid === order.orderUuid}
          />
        )}
        loading={loading}
        hasMore={hasMore}
        onLoadMore={handleLoadMore}
        layout="list"
        emptyText={i18next.t('j-dingtalk-web_pages_order-list_NoPurchaseHistory')}
        emptyIcon={<OrderOutlined />}
        noMoreText={i18next.t('j-dingtalk-web_pages_order-list_AllOrdersDisplayed')}
      />
    );
  };

  // Render order list view
  const renderOrderList = () => (
    <div className="order-list-view">
      <div className="page-header">
        <h1 className="page-title">{i18next.t('j-dingtalk-web_pages_order-list_MyOrders')}</h1>
      </div>

      <div className="order-list-container">{renderOrderListContent()}</div>
    </div>
  );

  // Render order detail view
  const renderOrderDetail = () => {
    if (!selectedOrder) return null;

    return <OrderDetail orderInfo={selectedOrder} onBack={handleBackToList} />;
  };

  // Render PC layout (left: order list, right: order detail)
  const renderPCLayout = () => (
    <div className="pc-layout">
      {/* Left side - Order List */}
      <div className="pc-left-panel">
        <div className="pc-header">
          <h1 className="pc-title">{i18next.t('j-dingtalk-web_pages_order-list_MyOrders')}</h1>
        </div>
        <div className="pc-order-list-container">{renderOrderListContent(true)}</div>
      </div>

      {/* Right side - Order Detail */}
      <div className="pc-right-panel">
        {selectedOrder ? (
          <OrderDetail orderInfo={selectedOrder} onBack={handleBackToList} isPCMode />
        ) : (
          <div className="pc-no-order-selected">
            <OrderOutlined />
            <p>{i18next.t('j-dingtalk-web_pages_order-list_PleaseSelectAnOrderTo')}</p>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile Navbar - Fixed at the top, outside of the main container */}
      {currentView === 'list' ? (
        <MobileNavbar
          title={i18next.t('j-dingtalk-web_pages_order-list_MyOrders')}
          showShareButton
        />
      ) : (
        <MobileNavbar
          title={i18next.t('j-dingtalk-web_pages_order-list_components_OrderDetail_OrderDetails')}
          showShareButton={false}
          onBackClick={handleBackToList}
        />
      )}

      <div className="order-list-page">
        {/* Mobile view */}
        <div className="mobile-view">
          {currentView === 'list' ? renderOrderList() : renderOrderDetail()}
        </div>

        {/* PC view */}
        <div className="pc-view">{renderPCLayout()}</div>
      </div>
    </>
  );
};

export default OrderListPage;
