// Order status types
export type OrderStatus = 'new' | 'running' | 'agree' | 'disagree' | 'cancel';

// API request and response types
export interface PageResourceOrderRequest {
  resourceKey: string;
  currentPage: number;
  pageSize: number;
}

export interface OrderData {
  orderUuid: string;
  orderData?: string;
  status: OrderStatus;
  resourceKey: string;
  orderQuota: number;
  gmtCreate: Date;
  gmtModified: Date;
}

export interface PageResourceOrderResponse {
  success: boolean;
  currentPage: number;
  pageSize: number;
  totalCount: number;
  data: OrderData[];
  errorMsg: string;
}

// Order detail response data
export interface ResourceOrderDetailData {
  orderUuid: string;
  orderData?: string;
  status: OrderStatus;
  resourceKey: string;
  orderQuota: number;
  gmtCreate: Date;
  gmtModified: Date;
}

// Order detail API response
export interface ResourceOrderDetailResponse {
  success: boolean;
  data: ResourceOrderDetailData;
  errorMsg: string;
}

// Order information interface (for UI display)
export interface OrderInfo {
  orderUuid: string;
  orderData?: string;
  orderQuota: number;
  gmtCreate: Date;
  gmtModified: Date;
  status: OrderStatus;
}

// Order detail page props
export interface OrderDetailProps {
  orderInfo: OrderInfo;
  onBack: () => void;
  isPCMode?: boolean; // Add PC mode flag
}

// Order list item props
export interface OrderListItemProps {
  orderInfo: OrderInfo;
  onClick: (orderInfo: OrderInfo) => void;
  isSelected?: boolean; // Add selected state flag
}
