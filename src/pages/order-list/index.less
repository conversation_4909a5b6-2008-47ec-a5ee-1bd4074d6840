// Import dingtalk theme
@import (reference) '~dingtalk-theme/dingtalk-x/mob.less';

// Enhanced scrollbar hiding mixin
.hide-scrollbar() {
  // Webkit browsers (Chrome, Safari, Edge)
  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    display: none;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    display: none;
    background: transparent;
  }

  &::-webkit-scrollbar-corner {
    display: none;
    background: transparent;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-track-piece {
    display: none;
    background: transparent;
  }

  // Firefox scrollbar hiding
  scrollbar-width: none;
  scrollbar-color: transparent transparent;

  // IE/Edge scrollbar hiding
  -ms-overflow-style: none;

  // Additional fallback
  overflow: -moz-scrollbars-none;
}

// Global scrollbar hiding for body, html and all elements
html,
body {
  .hide-scrollbar();
}

// Additional global scrollbar hiding for all elements
* {
  &::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

// Specific rules for PC layout scrollable containers
.pc-layout {
  * {
    &::-webkit-scrollbar {
      display: none !important;
      width: 0 !important;
    }

    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
}

.order-list-page {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background: radial-gradient(
      ellipse 600px 500px at top right,
      transparent 0%,
      transparent 30%,
      rgba(20, 20, 20, 0.1) 35%,
      rgba(20, 20, 20, 0.3) 45%,
      rgba(20, 20, 20, 0.5) 55%,
      rgba(20, 20, 20, 0.7) 65%,
      rgba(20, 20, 20, 0.85) 75%,
      #141414 85%
    ),
    linear-gradient(180deg, #6b1538 10%, #2f1624 100%);
  backdrop-filter: blur(40px);

  // PC端禁止整体页面滚动
  @media (min-width: 769px) {
    height: 100vh;
    overflow: hidden;
  }

  // 添加模糊过渡效果的伪元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 70%;
    height: 60%;
    background: radial-gradient(
      ellipse at top right,
      rgba(107, 21, 56, 0.15) 0%,
      rgba(107, 21, 56, 0.1) 20%,
      rgba(47, 22, 36, 0.08) 40%,
      rgba(47, 22, 36, 0.05) 60%,
      transparent 80%
    );
    filter: blur(50px);
    pointer-events: none;
    z-index: 1;
  }

  // 添加第二层更柔和的模糊效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 80%;
    height: 70%;
    background: radial-gradient(
      ellipse at top right,
      rgba(107, 21, 56, 0.08) 0%,
      rgba(47, 22, 36, 0.05) 30%,
      rgba(20, 20, 20, 0.03) 50%,
      transparent 70%
    );
    filter: blur(80px);
    pointer-events: none;
    z-index: 0;
  }

  .infinite-list-page {
    background-color: transparent;
    padding-bottom: 0;
  }

  .loading-container {
    min-height: -webkit-fill-available;
  }
}

// Order List View Styles
.order-list-view {
  min-height: 100vh;
  padding-top: calc(44px + env(safe-area-inset-top));

  // Mobile responsive adjustments
  @media (max-width: 768px) {
    padding-top: calc(44px + env(safe-area-inset-top));
  }

  // PC display rules
  @media (min-width: 769px) {
    padding-top: 0;
  }
}

.page-header {
  padding: 8px 16px 24px;

  .page-title {
    font-size: 24px;
    line-height: 34px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
  }

  // Hide on mobile, show on PC
  @media (max-width: 768px) {
    display: none;
  }

  @media (min-width: 769px) {
    display: block;
  }
}

.order-list-container {
  padding: 16px 16px 24px;

  // Error state
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 60px 0;

    .error-message {
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
      margin-bottom: 24px;
      text-align: center;
      color: rgba(255, 255, 255, 0.9);
    }

    .retry-button {
      background: #2e2e2f;
      border-radius: 8px;
      padding: 8px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #3e3e3f;
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }

  // Empty state
  .infinite-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 60px 0;

    .empty-icon {
      width: 48px;
      height: 48px;
      font-size: 48px;
      margin-bottom: 16px;
      color: rgba(255, 255, 255, 0.4);
    }

    .empty-text {
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
      margin: 0;
      text-align: center;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  // Pagination
  .pagination-container {
    display: flex;
    justify-content: center;
    padding: 24px 0;

    .load-more-button {
      background: #2e2e2f;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      padding: 12px 24px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background: #3e3e3f;
      }

      &:active:not(:disabled) {
        transform: translateY(1px);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .order-list-item {
    background: #2e2e2f;
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #2e2e2f;
      opacity: 0.8;
    }

    .order-header {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 40px;

      .order-number,
      .order-time {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .label {
          margin-right: 8px;
          min-width: 80px;
        }

        .value {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .order-time {
        margin-bottom: 0;
      }
    }

    .order-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .order-status {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: rgba(255, 255, 255, 0.9);
      }

      .product-price {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: rgba(255, 255, 255, 0.9);

        .label {
          margin-right: 8px;
        }
      }
    }

    // Status-based styling for order items
    &.order-status-new {
      .order-status {
        color: #ff0e53;
      }
    }

    &.order-status-running {
      .order-status {
        color: #ff7621;
      }
    }

    &.order-status-agree {
      .order-status {
        color: #0fffc3;
      }
    }

    &.order-status-disagree,
    &.order-status-cancel {
      .order-status {
        color: rgba(255, 255, 255, 0.4);
      }

      // Special styling for failed orders - make all text more transparent
      .order-header,
      .order-footer {
        .label,
        .value {
          color: rgba(255, 255, 255, 0.6);
        }
      }

      .product-price {
        color: rgba(255, 255, 255, 0.6);
      }
    }

    // Selected state styling
    &.selected {
      background: #2e2e2f;
      border: 1px solid #2e2e2f;

      &:hover {
        background: #2e2e2f;
        border: 1px solid #2e2e2f;
      }
    }
  }
}

// Order Detail Page Styles
.order-detail-page {
  min-height: 100vh;
  background: #1e1e1f;
  color: rgba(255, 255, 255, 0.9);
  padding-top: calc(44px + env(safe-area-inset-top));
  padding-bottom: calc(80px + env(safe-area-inset-bottom));
}

.status-header {
  padding: 24px 16px 32px;

  .status-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
    line-height: 40px;

    .countdown-timer {
      color: #ff0e53;
      margin-left: 8px;
      animation: countdown-pulse 1s ease-in-out infinite alternate;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
      min-width: 120px;
      display: inline-block;
      text-align: left;
    }
  }

  .status-subtitle {
    font-size: 16px;
    font-weight: 400;
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    line-height: 24px;
  }
}

// Countdown animation
@keyframes countdown-pulse {
  from {
    opacity: 0.8;
  }
  to {
    opacity: 1;
  }
}

.info-card {
  margin: 0 16px 16px;
  background: #2e2e2f;
  border-radius: 16px;
  padding: 16px;

  &.bank-info-card {
    .bank-logo {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 12px;

      .mizuho-logo {
        width: 48px;
        height: 48px;
        font-size: 0;
        img {
          width: 100%;
          height: 100%;
        }
      }

      .icon-copy {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.5);
        background: none;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
          transform: scale(1.1);
        }

        &:active {
          color: rgba(255, 255, 255, 0.5);
          transform: scale(0.9);
        }
      }
    }
  }

  .info-row {
    display: flex;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 14px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.6);
      min-width: 88px;
      flex-shrink: 0;
      line-height: 20px;
    }

    .value {
      font-size: 14px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.9);
      flex: 1;
      margin-right: 8px;
      line-height: 20px;
      word-break: break-all;
    }

    .copy-btn {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.4);
      cursor: pointer;
      padding: 2px;
      border-radius: 4px;
      transition: all 0.2s ease;
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
        background: rgba(255, 255, 255, 0.08);
      }

      &:active {
        transform: scale(0.9);
      }
    }
  }

  &.order-info-card {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .icon-copy {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.5);
      background: none;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
        transform: scale(1.1);
      }

      &:active {
        color: rgba(255, 255, 255, 0.5);
        transform: scale(0.9);
      }
    }
  }
}

.transfer-receipt-section {
  margin: 0 16px 16px;
  display: flex;
  flex-direction: row;
  gap: 16px;

  .section-left {
    flex: 1;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 6px;

    .title-text {
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
      margin-right: 8px;
      color: rgba(255, 255, 255, 0.9);
    }

    .example-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      background: none;
      border: 1px solid #ff0e53;
      color: #ff0e53;
      padding: 3px 6px;
      border-radius: 40px;
      white-space: nowrap;
      font-size: 12px;
      font-weight: 500;
      line-height: 18px;
      cursor: pointer;
      transition: all 0.2s ease;

      svg {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }

      &:hover {
        background: rgba(255, 14, 83, 0.1);
      }
    }
  }

  .receipt-tip {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    line-height: 18px;
  }

  .upload-area {
    position: relative;
    cursor: pointer;

    // Override ImageUploader component styles to match design
    .image-uploader {
      .upload-area {
        width: 80px;
        height: 80px;
        min-height: 80px;
        border-radius: 16px;
        background: #2e2e2f;
        border: 1px solid #2e2e2f;

        &:hover {
          background: lighten(#2e2e2f, 2%);
        }

        &:active {
          background: darken(#2e2e2f, 2%);
        }

        .upload-icon {
          font-size: 20px;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 8px;
        }

        .upload-text {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.9);
          line-height: 18px;
        }
      }

      .uploaded-image {
        width: 80px;
        height: 80px;
        border-radius: 24px;
        background: rgba(255, 255, 255, 0.08);

        .preview-img {
          object-fit: cover;
        }

        .image-actions {
          .reupload-btn {
            font-size: 12px;
          }
        }
      }
    }

    // Delete image overlay
    .delete-image-overlay {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      z-index: 10;

      .delete-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4px;
        background: rgba(0, 0, 0, 0.5);
        color: rgba(255, 255, 255, 0.9);
        font-size: 12px;
        cursor: pointer;

        &:hover {
          background: rgba(0, 0, 0, 0.6);
          color: #fff;
        }

        svg {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }
  }
}

.submit-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: calc(24px + env(safe-area-inset-bottom));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 16px;
  height: 44px;
  background: #ff0e53;
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 40px;
  z-index: 1000;
  border: none;

  &:hover:not(:disabled) {
    background: #e60026;
    transform: translateY(-1px);
  }

  &:active:not(:disabled) {
    background: #cc001f;
    transform: translateY(0);
  }

  &:disabled {
    background: #999999;
    cursor: not-allowed;
    transform: none;
  }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .order-list-page {
    overflow-x: hidden;
  }

  .order-list-view {
    padding-top: calc(44px + env(safe-area-inset-top));
  }

  .order-detail-page {
    padding-top: calc(44px + env(safe-area-inset-top));
  }

  .status-header {
    padding: 8px 16px 24px;

    .status-title {
      font-size: 24px;
      font-weight: 500;
      line-height: 34px;
      margin-bottom: 8px;

      .countdown-timer {
        color: #ff0e53;
        margin-left: 8px;
        animation: countdown-pulse 1s ease-in-out infinite alternate;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
        min-width: 90px;
        display: inline-block;
        text-align: left;
      }
    }

    .status-subtitle {
      font-size: 14px;
      line-height: 18px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);

      span {
        color: #ff0e53;
      }
    }
  }

  .info-card {
    .info-row {
      flex-direction: row;
      align-items: flex-start;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        min-width: auto;
      }

      .value {
        margin-right: 0;
        width: 100%;
      }

      .copy-btn {
        align-self: flex-end;
      }
    }
  }
}

// PC Layout Styles
.pc-view {
  display: none;

  @media (min-width: 769px) {
    display: block;
  }
}

.mobile-view {
  display: block;

  .infinite-list-list-container {
    padding: 0;
    gap: 0;
  }

  @media (min-width: 769px) {
    display: none;
  }
}

.pc-layout {
  display: flex;
  height: 100vh; // 使用固定高度而不是 min-height
  overflow: hidden; // 防止整体页面滚动

  // Left Panel - Order List
  .pc-left-panel {
    width: 400px;
    min-width: 400px;
    height: 100vh; // 设置固定高度
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(20, 20, 20, 0.3);
    backdrop-filter: blur(20px);
    overflow: hidden; // 防止面板本身滚动
    display: flex;
    flex-direction: column;

    .pc-header {
      padding: 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(20, 20, 20, 0.5);
      flex-shrink: 0; // 防止头部缩放

      .pc-title {
        font-size: 20px;
        line-height: 28px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }
    }

    .pc-order-list-container {
      flex: 1;
      overflow-y: auto; // 允许垂直滚动
      overflow-x: hidden; // 隐藏水平滚动
      padding: 12px 20px 16px 16px; // 右边留更多空间，避免内容靠近滚动条
      .hide-scrollbar(); // 隐藏滚动条

      // 强制隐藏滚动条的额外样式
      &::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
      }

      scrollbar-width: none !important;
      -ms-overflow-style: none !important;
    }

    .infinite-list-list-container {
      padding: 0;
    }

    // Empty state
    .infinite-list-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .empty-icon {
        width: 48px;
        height: 48px;
        font-size: 48px;
        margin-bottom: 16px;
        color: rgba(255, 255, 255, 0.4);
      }

      .empty-text {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        margin: 0;
        text-align: center;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  // Right Panel - Order Detail
  .pc-right-panel {
    flex: 1;
    height: 100vh; // 设置固定高度
    padding: 16px 20px 16px 12px; // 右边留更多空间，避免内容靠近滚动条
    overflow-y: auto; // 允许垂直滚动
    overflow-x: hidden; // 隐藏水平滚动
    position: relative;
    .hide-scrollbar(); // 隐藏滚动条

    // 强制隐藏滚动条的额外样式
    &::-webkit-scrollbar {
      display: none !important;
      width: 0 !important;
    }

    scrollbar-width: none !important;
    -ms-overflow-style: none !important;

    .pc-no-order-selected {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      svg {
        width: 48px;
        height: 48px;
        font-size: 48px;
        margin-bottom: 16px;
        color: rgba(255, 255, 255, 0.4);
      }

      p {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        margin: 0;
        text-align: center;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}

// PC Mode Order Detail Styles
.order-detail-page.pc-mode {
  padding: 0;
  max-width: 700px;
  min-height: calc(100vh - 56px);
  background: transparent;

  .status-header {
    padding: 0 0 24px;

    .status-title {
      font-size: 24px;
      line-height: 32px;
      margin-bottom: 8px;

      .countdown-timer {
        color: #ff0e53;
        margin-left: 8px;
        animation: countdown-pulse 1s ease-in-out infinite alternate;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
        min-width: 90px;
        display: inline-block;
        text-align: left;
      }
    }

    .status-subtitle {
      font-size: 14px;
      line-height: 18px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);

      span {
        color: #ff0e53;
      }
    }
  }

  .info-card {
    margin: 0 0 20px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    padding: 20px;

    &.bank-info-card {
      .bank-details {
        .info-row {
          display: flex;
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 20px;
        }
      }
    }

    &.order-info-card {
      position: relative;

      .info-row-container {
        .info-row {
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }

  .transfer-receipt-section {
    margin: 0 0 24px 0;
  }

  .submit-btn {
    position: initial;
    margin: 80px 0 16px 0;
    float: right;
    width: 340px;
  }
}

// Global Order List Item Styles (applies to both mobile and PC)
.order-list-item {
  background: #2e2e2f;
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 8px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #2e2e2f;
    opacity: 0.8;
  }

  .order-header {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;

    .order-number,
    .order-time {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .label {
        margin-right: 8px;
        min-width: 80px;
      }

      .value {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .order-time {
      margin-bottom: 0;
    }
  }

  .order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .order-status {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: rgba(255, 255, 255, 0.9);
    }

    .product-price {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: rgba(255, 255, 255, 0.9);

      .label {
        margin-right: 8px;
      }
    }
  }

  // Status-based styling for order items
  &.order-status-new {
    .order-status {
      color: #ff0e53;
    }
  }

  &.order-status-running {
    .order-status {
      color: #ff7621;
    }
  }

  &.order-status-agree {
    .order-status {
      color: #0fffc3;
    }
  }

  &.order-status-disagree,
  &.order-status-cancel {
    .order-status {
      color: rgba(255, 255, 255, 0.4);
    }

    // Special styling for failed orders - make all text more transparent
    .order-header,
    .order-footer {
      .label,
      .value {
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .product-price {
      color: rgba(255, 255, 255, 0.6);
    }
  }

  // Selected state styling
  &.selected {
    background: #353638;
    border: 1px solid #ffffff;

    &:hover {
      background: #353638;
      border-color: 1px solid #ffffff;
    }
  }
}

// PC Error state - same as empty state
.pc-error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 60px 0;

  .error-message {
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
    margin-bottom: 24px;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
  }

  .retry-button {
    background: #2e2e2f;
    border-radius: 8px;
    padding: 8px 16px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #3e3e3f;
    }

    &:active {
      transform: translateY(1px);
    }
  }
}
