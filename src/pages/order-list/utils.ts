import { i18next } from '@ali/dingtalk-i18n';
import { OrderStatus } from './types';

/**
 * Format order status for display
 */
export const formatOrderStatus = (status: OrderStatus): string => {
  const statusMap: Record<OrderStatus, string> = {
    new: i18next.t('j-dingtalk-web_pages_order-list_utils_PendingPayment'),
    running: i18next.t('j-dingtalk-web_pages_order-list_utils_UnderReview'),
    agree: i18next.t('j-dingtalk-web_pages_order-list_utils_Approved'),
    disagree: i18next.t('j-dingtalk-web_pages_order-list_utils_ReviewFailed'),
    cancel: i18next.t('j-dingtalk-web_pages_order-list_OrderCanceled'),
  };
  return statusMap[status] || i18next.t('j-dingtalk-web_pages_order-list_utils_UnknownStatus');
};
