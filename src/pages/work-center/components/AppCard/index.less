@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.app-card {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
  text-align: center;
  display: flex;
  align-items: center;
  overflow: hidden;

  &__icon {
    font-size: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    width: 40px;
    border-radius: 33%;
  }

  &__name {
    .common_body_text_style_mob();
    color: @common_level1_base_color;
    line-height: 18px;
    font-size: 14px;
    text-align: left;
    margin-left: 8px;

    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
