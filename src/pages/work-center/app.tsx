import { createRoot } from 'react-dom/client';
import '@/utils/init';
import { ConfigProvider } from 'dingtalk-design-desktop';
import WorkCenter from './index';
import '@/common/styles/global.less';
import { installAllPolyfill } from '@/common/polyfill';

const main = async () => {
  await installAllPolyfill();
  const root = createRoot(document.getElementById('Root'));
  root.render(
    <ConfigProvider>
      <WorkCenter />
    </ConfigProvider>,
  );
};

main();
