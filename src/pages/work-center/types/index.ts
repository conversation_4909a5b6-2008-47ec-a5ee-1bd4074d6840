// 应用入口类型定义
export interface IAppEntry {
  id: string;
  name: string;
  icon?: string;
  url?: string;
  onClick?: (app: IAppEntry) => void;
  description?: string;
  badge?: string | number;
  disabled?: boolean;
  thumb?: string;
}

// 应用分类类型定义
export interface IAppCategory {
  id: string;
  title: string;
  apps: IAppEntry[];
  type?: 'thumb-show';
  description?: string;
  topApp?: IAppEntry;
}

// 应用配置类型
export interface IAppConfig {
  categories: IAppCategory[];
  version: string;
  lastUpdated: string;
}
