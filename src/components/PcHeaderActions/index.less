.pc-header-actions {
  position: fixed;
  top: 24px;
  right: 24px;
  display: flex;
  align-items: center;
  gap: 32px;
  z-index: 1000;

  .usage-info {
    position: initial;
    transform: initial;
    padding: 7px 22px;
    background: rgba(0, 0, 0, 0.4);
  }

  .action-buttons {
    display: flex;
    gap: 32px;

    .action-btn {
      background: transparent;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }

      .btn-icon {
        color: rgba(255, 255, 255, 0.9);
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }
  }
}

// Mobile 端隐藏 PC 端功能区域
@media (max-width: 768px) {
  .pc-header-actions {
    display: none;
  }
}
