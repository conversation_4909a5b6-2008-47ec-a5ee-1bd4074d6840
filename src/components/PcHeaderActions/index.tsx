import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { OrderOutlined, ShareOutlined } from '@ali/ding-icons';
import { isDingTalk } from '@/utils/jsapi';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import { Toast } from 'dingtalk-design-mobile';
import FeaturePromotion from '@/components/FeaturePromotion';
import { QuotaUsageResponse } from '@/apis/quota';
import './index.less';

interface PcHeaderActionsProps {
  quotaUsage: QuotaUsageResponse;
  onOrderClick: () => void;
  shareConfig: {
    title: string;
    content: string;
    image: string;
  };
  className?: string;
}

const PcHeaderActions: React.FC<PcHeaderActionsProps> = ({
  quotaUsage,
  onOrderClick,
  shareConfig,
  className = '',
}) => {
  // 处理分享功能
  const handleShare = () => {
    if (isDingTalk()) {
      // 在钉钉端内使用钉钉分享 API
      $setShare({
        type: 0,
        url: window.location.href,
        title: shareConfig.title,
        content: shareConfig.content,
        image: shareConfig.image,
      });
    } else if (navigator.share) {
      // 在支持原生分享的浏览器中使用原生分享
      navigator.share({
        title: shareConfig.title,
        text: shareConfig.content,
        url: window.location.href,
      });
    } else {
      // 在不支持原生分享的浏览器中复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href).then(() => {
        Toast.success({
          content: i18next.t('j-dingtalk-web_components_PcHeaderActions_TheLinkHasBeenCopied'),
          position: 'top',
          maskClickable: true,
          duration: 2,
        });
      }).catch(() => {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_components_PcHeaderActions_ReplicationFailedPleaseCopyThe'),
          position: 'top',
          maskClickable: true,
          duration: 2,
        });
      });
    }
  };

  return (
    <div className={`pc-header-actions ${className}`}>
      <FeaturePromotion quotaUsage={quotaUsage} className="usage-info" />
      <div className="action-buttons">
        <button className="action-btn" onClick={onOrderClick}>
          <OrderOutlined className="btn-icon" />
        </button>
        <button className="action-btn" onClick={handleShare}>
          <ShareOutlined className="btn-icon" />
        </button>
      </div>
    </div>);
};

export default PcHeaderActions;
