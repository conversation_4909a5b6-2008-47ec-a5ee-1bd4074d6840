.free-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(20px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.free-modal {
  position: relative;
  width: 280px;
  max-width: 90vw;

  .free-modal-content {
    background: url('https://img.alicdn.com/imgextra/i2/O1CN01qth3K21Ur6ycf8u0A_!!6000000002570-2-tps-840-1044.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    backdrop-filter: blur(70px);
    border-radius: 24px;
    padding: 24px 24px 44px;
    text-align: center;
    position: relative;
    overflow: hidden;
    
    // Decorative elements
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 80px;
      background: linear-gradient(180deg, #19E0FF 0%, #735FE6 50%, linear-gradient(180deg, #F11560 0%, #FF8400 100%), 100%);
      pointer-events: none;
    }
  }
  
  .free-badge {
    width: 80px;
    height: 80px;
    font-size: 0;
    margin: 0 auto;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .free-modal-text {
    margin-top: 24px;
    margin-bottom: 44px;

    .free-title,
    .free-subtitle {
      font-size: 32px;
      line-height: 48px;
      color: #FFFFFF;
      font-weight: 900;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .free-contributor {
      margin-top: 12px;
      font-size: 12px;
      line-height: 18px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .receive-button {
    background: #FF0E53;
    border-radius: 25px;
    padding: 12px;
    margin: 0 auto;
    font-size: 16px;
    line-height: 24px;
    width: 165px;
    color: #FFFFFF;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.24s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 20px rgba(233, 30, 99, 0.5);
    }
    
    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(233, 30, 99, 0.6);
    }

    // Loading state styles
    &.loading {
      cursor: not-allowed;
      opacity: 0.8;
      transform: none;
      box-shadow: none;

      &:hover {
        transform: none;
        box-shadow: none;
      }

      &:active {
        transform: none;
        box-shadow: none;
      }
    }

    // Loading spinner animation
    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid #FFFFFF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
  
  .close-button {
    position: absolute;
    bottom: -68px;
    left: 50%;
    transform: translateX(-50%);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    
    .anticon {
      font-size: 44px;
      color: rgba(255, 255, 255, 0.9);
    }
    
    &:hover {
      color: #FFFFFF;
      transform: translateX(-50%) scale(1.05);
    }
    
    &:active {
      transform: translateX(-50%) scale(0.95);
    }
  }
}

// Animation for modal appearance
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.free-modal-overlay {
  animation: fadeIn 0.24s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.free-modal {
  animation: fadeIn 0.24s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// Loading spinner keyframes
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
