import { FC } from 'react';
import { CloseCircleOutlined } from '@ant-design/icons';
import { i18next } from '@ali/dingtalk-i18n';
import { FreeQuotaAccessResponse } from '@/apis/quota';
import './index.less';

interface FreeModalProps {
  visible: boolean;
  quotaAccessData: FreeQuotaAccessResponse;
  onClose: () => void;
  onReceive?: () => void;
  loading?: boolean; // Add loading prop
}

const FreeModal:
FC<FreeModalProps> = ({ visible, quotaAccessData, onClose, onReceive, loading = false }) => {
  if (!visible) return null;

  const handleReceiveClick = () => {
    if (loading) return; // Prevent click when loading
    onReceive?.();
  };

  return (
    <div className="free-modal-overlay" onClick={onClose}>
      <div className="free-modal" onClick={(e) => e.stopPropagation()}>
        <div className="free-modal-content">
          {/* Free badge */}
          <div className="free-badge">
            <img
              src="https://img.alicdn.com/imgextra/i2/O1CN01CZcMwS1mC2Xdvywc3_!!6000000004917-2-tps-320-320.png"
              alt="free"
            />
          </div>

          {/* Main content */}
          <div className="free-modal-text">
            <div className="free-title">
              {i18next.t('j-dingtalk-web_components_FreeModal_FreeGift')}
            </div>
            <div className="free-subtitle">
              {i18next.t('j-dingtalk-web_components_FreeModal_TrialCard')}
            </div>
            <div className="free-contributor">{i18next.t('j-dingtalk-web_components_FreeModal_By')}
              {quotaAccessData?.source === 'system' ? i18next.t('j-dingtalk-web_components_FreeModal_Official') : 'KANEYAO'}{i18next.t('j-dingtalk-web_components_FreeModal_Gift')}
            </div>
          </div>

          {/* Receive button */}
          <div
            className={`receive-button ${loading ? 'loading' : ''}`}
            onClick={handleReceiveClick}
          >
            {loading ?
              <>
                <div className="loading-spinner" />
                {i18next.t('j-dingtalk-web_components_FreeModal_Receiving')}
              </> : i18next.t('j-dingtalk-web_components_FreeModal_ReceiveNow')
            }
          </div>
        </div>

        {/* Close button */}
        <div className="close-button" onClick={onClose}>
          <CloseCircleOutlined />
        </div>
      </div>
    </div>);
};

export default FreeModal;
