import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { QuotaUsageResponse } from '@/apis/quota';
import { getCreateOrderUrl } from '@/utils/env';
import { isDingTalk, openDualLink } from '@/utils/jsapi';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import QuotaDisplay from '@/components/QuotaDisplay';
import './index.less';

interface FeaturePromotionProps {
  quotaUsage: QuotaUsageResponse | null;
  className?: string;
}

const FeaturePromotion: React.FC<FeaturePromotionProps> = ({
  quotaUsage,
  className = '',
}) => {
  // 处理订单列表按钮点击事件
  const handleClick = () => {
    // // Data tracking for order list click
    // sendUT('aigc_homepage_order_list_click', {
    //   device: isMobileDevice() ? 'mobile' : 'pc',
    // });

    const url = getCreateOrderUrl();
    if (isDingTalk()) {
      $openLink({
        url: openDualLink(url),
      });
    } else {
      window.open(url);
    }
  };

  return (
    <div
      className={`feature-promotion ${className}`}
      onClick={handleClick}
    >
      {i18next.t('j-dingtalk-web_components_FeaturePromotion_Usage')}
      <QuotaDisplay quotaUsage={quotaUsage} />
    </div>);
};

export default FeaturePromotion;
