.mobile-navbar {
  display: none; // Default hidden, show on mobile
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: transparent;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  padding-top: env(safe-area-inset-top);
  height: calc(44px + env(safe-area-inset-top));

  .nav-title {
    font-size: 18px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .nav-right-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 44px; // Keep minimum width to maintain layout balance
  }

  .nav-button {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    font-size: 22px;
    font-weight: bold;
    padding: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.7;
    }

    &:active {
      opacity: 0.5;
    }

    &.order-button {
      font-size: 20px; // Slightly smaller for the order list icon
    }
  }

  // Mobile display rules
  @media (max-width: 768px) {
    display: flex !important;
  }

  // Hide on PC
  @media (min-width: 769px) {
    display: none !important;
  }
}
