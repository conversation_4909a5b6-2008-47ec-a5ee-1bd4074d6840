import React from 'react';
import { NaviLeftArrowLOutlined, MoreOutlined, OrderOutlined } from '@ali/ding-icons';
import { closePanel, isDingTalk, openDualLink } from '@/utils/jsapi';
import { i18next } from '@ali/dingtalk-i18n';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { getOrderListUrl } from '@/utils/env';
import './index.less';

interface CustomButton {
  // Button icon (React component)
  icon: React.ReactNode;
  // Button text for accessibility
  text?: string;
  // Click handler
  onClick: () => void;
}

interface MobileNavbarProps {
  // Show order list button
  showOrderButton?: boolean;
  // Show share button
  showShareButton?: boolean;
  // Custom right buttons (will override share and order buttons)
  customRightButtons?: CustomButton[];
  // Custom back button click handler, if not provided, will use default closePanel
  onBackClick?: () => void;
  // Custom order button click handler
  onOrderClick?: () => void;
  // Custom share button click handler, if not provided, will use default share logic
  onShareClick?: () => void;
  // Share configuration for default share logic
  shareConfig?: {
    url?: string;
    title?: string;
    content?: string;
    image?: string;
  };
  // Title for the navbar
  title?: string;
}

const MobileNavbar: React.FC<MobileNavbarProps> = ({
  showOrderButton = false,
  showShareButton = true,
  customRightButtons,
  title,
  onBackClick,
  onOrderClick,
  onShareClick,
  shareConfig,
}) => {
  // Handle back button click
  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      closePanel();
    }
  };

  // Handle share button click
  const handleShareClick = () => {
    if (onShareClick) {
      onShareClick();
    } else if (isDingTalk() && shareConfig) {
      $setShare({
        type: 0,
        url: shareConfig.url || window.location.href,
        title: shareConfig.title || document.title,
        content: shareConfig.content || '',
        image: shareConfig.image || '',
      });
    }
  };

  // Handle order list button click
  const handleOrderListClick = () => {
    if (onOrderClick) {
      onOrderClick();
    } else {
      // Default order list navigation
      const url = getOrderListUrl();
      if (isDingTalk()) {
        $openLink({
          url: openDualLink(url),
        });
      } else {
        window.open(url);
      }
    }
  };

  return (
    <div className="mobile-navbar">
      <button className="nav-button back-button" onClick={handleBackClick}>
        <NaviLeftArrowLOutlined />
      </button>

      {title && (
        <div className="nav-title">
          {title}
        </div>
      )}

      <div className="nav-right-buttons">
        {customRightButtons ? (
          // If custom buttons are provided, use them instead of default buttons
          customRightButtons.map((button, index) => (
            <button
              key={`custom-btn-${index}`}
              className="nav-button custom-button"
              onClick={button.onClick}
              title={button.text}
            >
              {button.icon}
            </button>
          ))
        ) : (
          // Default buttons
          <>
            {showOrderButton && (
              <button
                className="nav-button order-button"
                onClick={handleOrderListClick}
                title={i18next.t('j-dingtalk-web_pages_ai-studio_OrderList')}
              >
                <OrderOutlined />
              </button>
            )}
            {showShareButton && (
              <button className="nav-button share-button" onClick={handleShareClick}>
                <MoreOutlined />
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default MobileNavbar;
