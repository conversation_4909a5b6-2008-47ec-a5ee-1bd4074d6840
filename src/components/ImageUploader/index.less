.image-uploader {
  position: relative;

  .upload-area {
    border: 1px solid #141414;
    border-radius: 16px;
    text-align: center;
    background: #141414;
    height: 202px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 188px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    outline: none; // Remove default focus outline
    position: relative;
    overflow: hidden;

    .upload-icon {
      font-size: 48px;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 16px;
    }

    .upload-text {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.5);
      font-weight: normal;
    }

    // &:hover {
    //   border: 1px solid #FF0E53;
    // }

    &:active {
      border: 1px solid #FF0E53;
    }

    // Focus state for accessibility and auto-focus
    &:focus {
      border: 1px solid #FF0E53;
    }

    // // Focus-visible for better accessibility (only show focus ring when navigating with keyboard)
    // &:focus-visible {
    //   border: 1px solid #FF0E53;
    // }
  }

  .uploaded-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid #141414;
    background: #141414;
    cursor: pointer;

    .preview-img-container {
      width: 200px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0;
      background:
        linear-gradient(45deg, #EBEBEB 25%, transparent 25%),
        linear-gradient(-45deg, #EBEBEB 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #EBEBEB 75%),
        linear-gradient(-45deg, transparent 75%, #EBEBEB 75%);
      background-size: 16px 16px;
      background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
      background-color: #FFFFFF;
    }

    .preview-img {
      max-width: 100%;
      max-height: 100%;
      width: 100%;
      height: 200px;
      object-fit: contain;
      display: block;
    }

    .image-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 20;

      .reupload-btn {
        min-width: 32px;
        height: 32px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 8px;
        border: none;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(20px);
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        cursor: pointer;

        &:hover {
          background: rgba(0, 0, 0, 0.6);
          color: white;
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  // Scanning overlay for image matting loading effect
  .scanning-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 15;
    overflow: hidden;
    border-radius: inherit;

    .scanning-line {
      position: absolute;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, transparent, #FF0E53, transparent);
      box-shadow: 0 0 10px #FF0E53;
      animation: scanning 2s linear infinite;
    }
  }

  .upload-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    z-index: 10;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #333333;
      border-top: 3px solid #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 12px;
    }

    span {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes scanning {
  0% {
    top: -3px;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}
