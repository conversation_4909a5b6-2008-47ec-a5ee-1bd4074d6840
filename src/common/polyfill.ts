import { isAndroid, isIOS } from '@ali/dingtalk-jsapi/plugin/environment';
import getPhoneInfo from '@ali/dingtalk-jsapi/api/device/base/getPhoneInfo';

async function installResizeObserver() {
  if (typeof window !== 'undefined' && !window.ResizeObserver) {
    const resizeObserver = await import(
      /* webpackChunkName: "resize-observer-polyfill" */ 'resize-observer-polyfill'
    );
    // eslint-disable-next-line require-atomic-updates
    window.ResizeObserver = resizeObserver.default;
  }
}

/**
 * 防止页面进入没有 safe area 而抖动
 * https://alidocs.dingtalk.com/i/nodes/P7QG4Yx2Jpx4OolYCyQDpq30J9dEq3XD
 * @returns
 */
export function installSafeAreaPolyfill() {
  if (!isIOS) {
    // 只处理 iOS
    return;
  }
  const isFullscreen = new URLSearchParams(location.search).get('dd_full_screen') === 'true';
  if (!isFullscreen) {
    // 只处理全屏显示
    return;
  }

  const KEY = '__SAFE_AREA_TOP_INSET_POLYFILL__';
  const top = localStorage.getItem(KEY);
  if (top) {
    const style = document.createElement('style');
    style.id = KEY;
    style.innerHTML = `:root { --safe-area-inset-top: ${top} !important; }`;
    document.head.prepend(style);
    return;
  }

  let running = true;
  setTimeout(() => {
    // 5s 后自动结束测量
    running = false;
  }, 5e3);

  function trySaveTopInset() {
    if (!running) return;

    const safeAreaInsetTop = getComputedStyle(document.documentElement)
      .getPropertyValue('--safe-area-inset-top')
      .trim();
    if (parseInt(safeAreaInsetTop, 10) > 1) {
      window.localStorage.setItem(KEY, safeAreaInsetTop);
      return;
    }
    requestAnimationFrame(trySaveTopInset);
  }
  trySaveTopInset();
}

/**
 * 安卓的 safe area
 * @returns
 */
export async function installNavTranslucentPolyfill() {
  if (!isAndroid) {
    // 只处理 android
    return;
  }
  const searchParams = new URLSearchParams(location.search);
  const isNavTranslucent =
    searchParams.get('dd_full_screen') === 'true' &&
    searchParams.get('dd_nav_translucent') === 'true';
  if (!isNavTranslucent) {
    // 只处理开启了顶部透明显示的情况
    return;
  }

  // 因为可能会存在调整该值的高度
  // 所以引入了版本的概念
  // 确保调整后，客户端能及时生效
  const version = 1;
  const KEY = `__SAFE_AREA_TOP_INSET_POLYFILL_v${version}__`;
  const top = localStorage.getItem(KEY);
  const isInitialized = Boolean(top);
  const safeAreaInsetTop = top || '44px';

  const style = document.createElement('style');
  style.id = KEY;
  style.innerHTML = `:root { --safe-area-inset-top: ${safeAreaInsetTop} !important; }`;
  document.head.prepend(style);

  if (isInitialized) {
    return;
  }

  const info = (await getPhoneInfo({})) as any as {
    screenScale: number;
    statusBarHeight: number;
  };
  if (info && info?.statusBarHeight && info?.screenScale) {
    window.localStorage.setItem(KEY, `${info.statusBarHeight / info.screenScale + 12}px`);
  }
}

/**
 * 主动按需安装一些 polyfill
 */
export async function installAllPolyfill() {
  try {
    // 该 polyfill 需要尽早运行
    installSafeAreaPolyfill();
    installNavTranslucentPolyfill();
    await installResizeObserver();
  } catch (e) {
    console.error('[installAllPolyfill] error ', e);
  }
}
