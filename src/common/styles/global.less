@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

// 深色模式使用文档：https://yuque.antfin-inc.com/docs/share/8b63de41-dd76-4fac-8a4d-ba806873a565
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif
}

:root {
  --custom_color: @common_fg_color;
}

:root[data-dingtalk-theme='dark'] {
  --custom_color: @common_level1_base_color;
}

// Global scrollbar styles for better appearance
* {
  // Custom scrollbar for webkit browsers
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: @common_level4_base_color;
    border-radius: @common_border_radius_s;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: @common_level3_base_color;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

// Dark mode scrollbar
:root[data-dingtalk-theme='dark'] {
  * {
    &::-webkit-scrollbar-thumb {
      background: @common_white4_color;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: @common_white3_color;
    }
  }
}

:root {
  --safe-area-inset-top: 0px;
  --safe-area-inset-right: 0px;
  --safe-area-inset-bottom: 0px;
  --safe-area-inset-left: 0px;

  @supports (top: constant(safe-area-inset-top)) {
    --safe-area-inset-top: constant(safe-area-inset-top);
    --safe-area-inset-right: constant(safe-area-inset-right);
    --safe-area-inset-bottom: constant(safe-area-inset-bottom);
    --safe-area-inset-left: constant(safe-area-inset-left);
  }

  @supports (top: env(safe-area-inset-top)) {
    --safe-area-inset-top: env(safe-area-inset-top, 0px);
    --safe-area-inset-right: env(safe-area-inset-right, 0px);
    --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
    --safe-area-inset-left: env(safe-area-inset-left, 0px);
  }
}
