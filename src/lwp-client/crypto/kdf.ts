export async function kdfSha256(seed, sid, keyLength) {
  // 将字符串转换为 Uint8Array
  const textEncoder = new TextEncoder();
  const salt = textEncoder.encode(sid); // 盐值 (salt)
  const ikm = textEncoder.encode(seed); // 输入密钥材料 (IKM)
  const info = new Uint8Array(); // 信息参数为空

  try {
    // 提取阶段 (Extract): 导入输入密钥材料 (IKM)
    const hkdfKey = await crypto.subtle.importKey(
      'raw', // 格式为原始字节
      ikm,
      { name: 'HKDF' }, // 算法名称
      false, // 是否可提取
      ['deriveBits'], // 允许的操作
    );

    // 扩展阶段 (Expand): 使用 HKDF 派生密钥
    const derivedBits = await crypto.subtle.deriveBits(
      {
        name: 'HKDF',
        hash: 'SHA-256', // 使用 SHA-256 哈希函数
        salt, // 盐值
        info, // 信息参数
      },
      hkdfKey, // 提取阶段生成的密钥
      keyLength * 8, // 输出长度（单位为比特）
    );

    // 转换为 Base64 编码
    const derivedKeyArray = new Uint8Array(derivedBits);
    const base64Key = btoa(String.fromCharCode(...derivedKeyArray));
    return base64Key;
  } catch (error) {
    console.error('Error generating key:', error);
    throw error;
  }
}
