/* eslint-disable @typescript-eslint/member-ordering */
import * as blake from 'blakejs';
import CryptoJS from 'crypto-js';
import stringify from 'fast-json-stable-stringify';
import { sha3_224, sha3_256, sha3_384, sha3_512 } from 'js-sha3';
import { kdfSha256 } from './crypto/kdf';
import { ILwpChannel_Request } from './types';

type HashAlgorithm = 'MD5' | 'SHA-1' | 'SHA-256' | 'SHA-224' | 'SHA-384' | 'SHA-512' | 'SHA3-256' | 'SHA3-224' | 'SHA3-384' | 'SHA3-512' | 'BLAKE2b-512' | 'BLAKE2b-256' | 'BLAKE2b-384';

function hexToBase64(hexString) {
  // 将 Hex 字符串转换为二进制字符串
  const binaryString = hexString
    .match(/.{1,2}/g)
    .map((byte) => String.fromCharCode(parseInt(byte, 16)))
    .join('');
  // 使用 btoa 将二进制字符串转换为 Base64
  return btoa(binaryString);
}

export interface SignerConfig {
  urlWhiteList?: string[];
  version: string;
  seed: string; // 种子
  hashAlg1: HashAlgorithm; // 用于计算 headers 的哈希算法
  hashAlg2: HashAlgorithm; // 用于计算 body 的哈希算法
  hashAlg3: HashAlgorithm; // 用于计算签名的哈希算法
  concatenationOrder: Array<
  'sessionKey' | 'canonicalUri' | 'hashedCanonicalBody' | 'nonce' | 'hashedCanonicalHeaders'
  >; // 拼接顺序
  signHeader: string[]; // ['sid', 'mid', 'ua', 'token', 'app-key', 'request-signature-version'];
  signValidMs: number; // 签名有效时间，默认1分钟，接入层会阻拦签名已过期的请求，ping时会给客户端返回服务端时间戳，客户端会尽量使用这个时间戳，validMs理论上不应该低于1min
}

interface WSContext {
  sid: string;
  ua: string;
  ['app-key']: string;
  token: string;
  ['server-timestamp']: number;
}

// 详细方案：https://alidocs.dingtalk.com/i/nodes/7dx2rn0Jbakrq9O5tLwKqxdXVMGjLRb3?utm_source=im&utm_scene=person_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&corpId=ding8196cd9a2b2405da24f2f5cc6abecb85
export class Signer {
  private config: SignerConfig;
  private context: Partial<WSContext> = {};

  constructor(config: SignerConfig) {
    this.config = config;
    this.validateConfig();
  }

  setContext(context: Partial<WSContext>) {
    this.context = {
      ...this.context,
      ...context,
    };
  }

  // 验证配置合法性
  private validateConfig() {
    const { concatenationOrder } = this.config;

    // 检查拼接顺序是否合法
    if (concatenationOrder.length !== 5) {
      throw new Error('Invalid concatenation_order length. It must contain exactly 5 elements.');
    }
  }

  // 计算哈希值
  private calculateHash(algorithm: HashAlgorithm, data: string): string {
    switch (algorithm) {
      case 'MD5':
        return CryptoJS.MD5(data).toString(CryptoJS.enc.Base64);
      case 'SHA-1':
        return CryptoJS.SHA1(data).toString(CryptoJS.enc.Base64);
      case 'SHA-256':
        return CryptoJS.SHA256(data).toString(CryptoJS.enc.Base64);
      case 'SHA-224':
        return CryptoJS.SHA224(data).toString(CryptoJS.enc.Base64);
      case 'SHA-384':
        return CryptoJS.SHA384(data).toString(CryptoJS.enc.Base64);
      case 'SHA-512':
        return CryptoJS.SHA512(data).toString(CryptoJS.enc.Base64);
      case 'SHA3-256':
        return hexToBase64(sha3_256(data));
      case 'SHA3-224':
        return hexToBase64(sha3_224(data));
      case 'SHA3-384':
        return hexToBase64(sha3_384(data));
      case 'SHA3-512':
        return hexToBase64(sha3_512(data));
      // 支持 BLAKE2b 算法
      case 'BLAKE2b-512':
        return this.hexToBase64(blake.blake2bHex(data, undefined, 64)); // 输出 512 位
      case 'BLAKE2b-256':
        return this.hexToBase64(blake.blake2bHex(data, undefined, 32)); // 输出 256 位
      case 'BLAKE2b-384':
        return this.hexToBase64(blake.blake2bHex(data, undefined, 48)); // 输出 384 位
      default:
        throw new Error(`Unsupported hash algorithm: ${algorithm}`);
    }
  }

  private hexToBase64(hex: string): string {
    return btoa(
      hex
        .match(/\w{2}/g)!
        .map((byte) => String.fromCharCode(parseInt(byte, 16)))
        .join(''),
    );
  }

  // 生成会话密钥 sessionKey 使用 HKDF
  private generateSessionKey = async (sid: string) => {
    return kdfSha256(this.config.seed, sid, 32);
  };

  // 生成 nonce
  private generateNonce(): string {
    const timestampSign = this.context['server-timestamp'] || Date.now(); // 默认 1 分钟有效时间
    const randNumber = Math.floor(Math.random() * 2 ** 32)
      .toString(16)
      .padStart(8, '0'); // 4 字节随机数
    return `${timestampSign}||${randNumber}`;
  }

  private generateHashedCanonicalHeaders(canonicalHeaders: Record<string, string>): string {
    let generateHashedCanonicalHeaders = '';
    this.config?.signHeader?.forEach((key) => {
      if (canonicalHeaders[key]) {
        generateHashedCanonicalHeaders += `${key}:${canonicalHeaders[key]}\n`;
      }
    });
    if (generateHashedCanonicalHeaders) {
      return this.calculateHash(this.config.hashAlg1, generateHashedCanonicalHeaders);
    } else {
      return '';
    }
  }

  private generateHashedCanonicalBody(canonicalBody: any[]): string {
    const jsoncanonicalBody = stringify(canonicalBody);
    return this.calculateHash(this.config.hashAlg2, jsoncanonicalBody); // body 哈希
  }

  // 加签主函数
  async signRequest(request: ILwpChannel_Request): Promise<ILwpChannel_Request> {
    // 白名单忽略
    if (this.config.urlWhiteList?.includes?.(request.lwp)) {
      return request;
    }

    const extraHeaders: Record<string, string> = {
      'cli-access-sign-ver': this.config.version,
    };

    // 计算会话密钥 sessionKey
    const sessionKey = await this.generateSessionKey(this.context.sid);

    // 规范化请求
    const nonce = this.generateNonce(); // 生成 nonce
    extraHeaders['cli-access-sign-nonce'] = nonce;

    this.config?.signHeader?.forEach((key) => {
      if (request.headers[key]) {
        extraHeaders[key] = request.headers[key];
      } else if (this.context[key]) {
        extraHeaders[key] = this.context[key];
      }
    });

    const hashedCanonicalHeaders = this.generateHashedCanonicalHeaders({
      ...request.headers,
      ...extraHeaders,
    }); // headers 哈希
    if (request.body) {
      const hashedCanonicalBody = request.body
        ? this.generateHashedCanonicalBody(request.body)
        : ''; // body 哈希

      // 根据拼接顺序生成 canonicalRequest
      const components = {
        sessionKey,
        canonicalUri: request.lwp,
        hashedCanonicalBody,
        nonce,
        hashedCanonicalHeaders,
      };
      const canonicalRequestParts = this.config.concatenationOrder.map((key) => components[key]);
      const canonicalRequest = canonicalRequestParts.join('\n');

      // 3. 计算签名 sign
      const sign = this.calculateHash(this.config.hashAlg3, canonicalRequest);
      extraHeaders['cli-access-sign-sign'] = sign;
    }

    // 强制开启
    extraHeaders['cli-access-sign-on'] = '1';

    // 返回结果
    return {
      ...request,
      headers: {
        ...request.headers,
        ...extraHeaders,
      },
    };
  }
}
