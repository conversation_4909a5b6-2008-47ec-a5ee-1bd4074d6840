import { WssMsgEncryptUtilsClass } from './encrypt';
import { KeepAlive } from './KeepAlive';
import { getCurrentLanguage } from './language';
import { LwpChannel } from './LwpChannel';
import { Signer, SignerConfig } from './sign';
import {
  ELwpChannel_EventLevel,
  ELwpChannel_EventType,
  ELwpChannel_ServerStatusCode,
  ILwpChannel_Request,
  ILwpChannel_RequestOption,
  ILwpChannel_Response,
  LwpRegAuthInfo,
  TLwpChannel_RequestHandler,
} from './types';

// ************************************
// LwpWebClient对象相关事件
// ************************************

// LwpWebClient事件类型
export enum ELwpWebClient_EventType {
  REG_SID = 'REG_SID', // 鉴权请求SID
  AUTHED = 'AUTHED', // 鉴权成功
  INVALID_TOKEN = 'INVALID_TOKEN', // TOKEN失效，需要重新登录
  AUTH_ERROR = 'AUTH_ERROR', // 未知原因reg失败
  AUTH_FAILED = 'AUTH_FAILED', // 业务方主动调用“退出”，被服务端踢出，登录态失效，需要重新登录
  DEVICE_LIMIT = 'DEVICE_LIMIT', // 登录的端数量超限（多端互踢），被服务端踢出，登录态失效
  CONNECT_LIMIT = 'CONNECT_LIMIT', // 连接数超过上限，被服务端踢出。可以根据实际需要重连
}

// LwpWebClient事件结构
export interface ILwpWebClient_Event {
  level: ELwpChannel_EventLevel;
  type: ELwpWebClient_EventType | ELwpChannel_EventType;
  data?: any;
}

// LwpWebClient事件监听函数结构定义
export type TLwpWebClient_EventListener = (event: ILwpWebClient_Event) => void;

// ************************************
// LwpWebClient初始化参数
// ************************************

export interface ILwpWebClientProps {
  wsUrl: string;
  appKey: string;
  lang: string;
  regType?: string; // '0'-主连接，同步协议保障不丢失消息; '1'-辅连接，支持多通道并存不互踢，但不保证不丢失推送消息
  aesEncryption?: boolean; // 是否开启AES加密
  extraUserAgent?: string;
  /**
   * 签名配置
   */
  signerConfig?: SignerConfig;
  /**
   * 获取连接信息
   * @returns
   */
  getRegAuthInfo: () => Promise<LwpRegAuthInfo>;
}

export class LwpWebClient {
  private static readonly KICKOUT_CHANNEL = '/push/kickout'; // 业务方主动调用“退出”，登录态失效，服务端踢出登录时推送的topic
  private static readonly KICKOUT_CHANNELV2 = '/push/kickoutV2'; // 登录的端数量超限（多端互踢），服务端踢出登录时推送的topic
  private static readonly SESSION_REMOVE = '/s/session/remove'; // 连接数超限，服务端踢出连接时推送的topic

  private readonly _wsUrl: string;
  private readonly _appKey: string;
  private readonly _regType: string;
  private readonly _aesEncryption: boolean;
  private readonly _lang: string;
  private readonly _extraUserAgent: string;

  // lwpChannel实例
  private readonly _lwpChannel: LwpChannel;
  // KeepAlive实例
  private readonly _keepAlive: KeepAlive;
  // AES加密实例
  private readonly _wssMsgEncrypt: WssMsgEncryptUtilsClass;

  private readonly _requestSigner: Signer | null = null;

  private _accessToken: string;
  private _isTokenValid: boolean;
  private _isKickout: boolean;
  private _deviceId?: string;
  private _lastRegInfo: any; // 上一次Reg成功的信息
  private _onError: (isLoginStatusValid: boolean, isKickout: boolean) => void;

  constructor(props: ILwpWebClientProps) {
    this._wsUrl = props.wsUrl;
    this._appKey = props.appKey;
    this._regType = props.regType || '1';
    this._aesEncryption = props.aesEncryption || false;
    this._extraUserAgent = props.extraUserAgent;
    this._lang = props.lang;

    if (this._aesEncryption) {
      this._wssMsgEncrypt = new WssMsgEncryptUtilsClass();
    }

    if (props.signerConfig) {
      this._requestSigner = new Signer(props.signerConfig);
    }

    this._lwpChannel = new LwpChannel({
      aesMsgEncryption: this._wssMsgEncrypt,
      signer: this._requestSigner,
      requestTimeout: 10000, // 默认请求超时时间10s
    });

    this._keepAlive = new KeepAlive({
      maxConnectCount: 3,
      connectCountIncInterval: 30000,
      heartbeatInterval: 10000,
      connect: async (onClose) => {
        if (this._isTokenValid) {
          try {
            const authInfo = await props.getRegAuthInfo();

            if (!authInfo.token) {
              this._isTokenValid = false;
              this._onError && this._onError(this._isTokenValid, this._isKickout);
              return Promise.reject();
            }

            await this._lwpChannel.connect(this._wsUrl, onClose);
            return this._auth(authInfo);
          } catch (e) {
            return Promise.reject();
          }
        } else {
          return Promise.reject();
        }
      },
      update: () => {
        return this._heartbeat();
      },
      onError: () => {
        this._onError && this._onError(this._isTokenValid, this._isKickout);
      },
    });

    // 监听服务端登录态失效推送
    this._bindInternalPushEventHandler();
  }

  // 建立连接并鉴权
  connect(
    onError: (isLoginStatusValid: boolean, isKickout: boolean) => void,
    // options?: { deviceId?: string },
  ) {
    // const { deviceId } = options || {};
    // if (!accessToken) {
    //   this._accessToken = '';
    //   this._deviceId = deviceId;
    //   this._isTokenValid = false;
    //   this._isKickout = false;
    //   this._onError = onError;
    //   this._triggerEvent(ELwpChannel_EventLevel.ERROR, ELwpWebClient_EventType.INVALID_TOKEN);
    //   onError(false, false);
    //   return Promise.reject();
    // } else {
    // this._accessToken = accessToken;
    // this._deviceId = deviceId;
    this._isTokenValid = true;
    this._isKickout = false;
    this._onError = onError;
    this._keepAlive.start();
    return this._keepAlive.waitAlive();
    // }
  }

  // 发送消息
  async sendMsg(
    request: ILwpChannel_Request,
    option?: ILwpChannel_RequestOption,
  ): Promise<ILwpChannel_Response> {
    try {
      await this._keepAlive.waitAlive();
      return this._lwpChannel.send(request, option);
    } catch (e) {
      return Promise.reject(ELwpChannel_EventType.SEND_ERROR);
    }
  }

  // 加密信息
  encryptMessage = (msg: Record<string, any>) => {
    if (!this._aesEncryption) {
      return msg;
    }
    return this._wssMsgEncrypt.encryptWssMsg(msg);
  };

  // 监听来自于服务端的推送消息。
  // 注意，一个topic只可以注册一个handler。
  // 因为要给服务端上报消息的处理状态，如果有多个handler，可能会导致返回状态混乱。
  on(topic: string, handler: TLwpChannel_RequestHandler) {
    this._lwpChannel.on(topic, handler);
  }

  off(topic: string) {
    this._lwpChannel.off(topic);
  }

  // 设置Client事件监听器，主要用于监听连接失败或授权失败的client事件和消息收发事件
  // 注意！！！listener只可以设置一个，后面设置的会覆盖前面的。
  listen(listener: TLwpWebClient_EventListener) {
    this._listener = listener;
    this._lwpChannel.listen(listener);
  }

  // 获取上一次Reg成功的信息
  // 注意，该信息不保证有效，因为有可能存在断线重连的情况，在重连过程中该信息是失效的
  getLastRegInfo() {
    return this._lastRegInfo;
  }

  // 外部传入的事件监听方法，一般用于通道异常处理或者打log
  private _listener: TLwpWebClient_EventListener = () => {};

  // 登录鉴权
  private _auth(connectInfo: LwpRegAuthInfo): Promise<void> {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      // const { deviceId } = options || {};
      const extra = connectInfo?.deviceId ? { did: connectInfo?.deviceId } : {};
      const keyExchange = this._aesEncryption
        ? await this._wssMsgEncrypt.generateECDHKey()
        : await Promise.resolve('');
      const aesParams = this._aesEncryption ? { 'key-exchange': keyExchange } : {};

      let ua = `${navigator.userAgent} App/7.6.0 DingWeb/7.6.0 LANG/${this._lang || getCurrentLanguage()}`;
      if (this._extraUserAgent) {
        ua = `${ua} ${this._extraUserAgent}`;
      }
      const headers = {
        'cache-header': 'app-key token ua wv',
        'app-key': this._appKey,
        token: connectInfo.token,
        ua,
        dt: 'j', // 数据格式，body的序列化方式，WebSocket只能用json
        wv: 'im:0,au:0,sy:10', // 同步协议版本，仅有sy参数有效
        sync: connectInfo.syncHeader || '0,0;0;0;', // 记录上一次同步的位置，web 端传 0
        'set-ver': '0', // Unused
        'reg-type': this._regType,
        ...extra,
      };

      let body: any[] | undefined;
      if (connectInfo.syncDeviceLabel) {
        headers['cache-header'] = 'app-key token ua wv sync-device-label';
        headers['sync-device-label'] = connectInfo.syncDeviceLabel;
      }

      if (connectInfo.syncContext) {
        body = [];
        body.push({
          syncContext: connectInfo.syncContext,
        });

        delete headers.sync;
      }
      this._lwpChannel
        .send({
          lwp: '/reg',
          headers,
          body,
          ...aesParams,
        })
        .then(async (resp) => {
          this._requestSigner?.setContext?.({
            sid: resp.headers.sid,
            ua,
            'app-key': this._appKey,
            token: connectInfo.token,
          });

          if (resp.code === ELwpChannel_ServerStatusCode.SUCCESS) {
            this._lastRegInfo = resp;
            if (this._aesEncryption) await this._wssMsgEncrypt.deriveAESKey(resp['key-exchange']);
            this._triggerEvent(ELwpChannel_EventLevel.INFO, ELwpWebClient_EventType.AUTHED, resp);
            resolve();
          } else if (resp.code === ELwpChannel_ServerStatusCode.NOT_AUTHED) {
            // token失效，鉴权失败
            this._onLoginStatusExpired(ELwpWebClient_EventType.INVALID_TOKEN, false);
            reject();
          } else {
            // 未知原因鉴权失败，这里不标注登录态失效
            reject();
          }

          this._triggerEvent(
            ELwpChannel_EventLevel.INFO,
            ELwpWebClient_EventType.REG_SID,
            resp.headers.sid,
          );
        })
        .catch(() => {
          // 未知原因鉴权失败，这里不标注登录态失效
          reject();
        });
    });
  }

  // 鉴权失败处理
  private _onLoginStatusExpired(reason: ELwpWebClient_EventType, isKickout: boolean) {
    // 标记当前登录态失效
    this._isTokenValid = false;
    this._isKickout = isKickout;
    this._triggerEvent(ELwpChannel_EventLevel.ERROR, reason);
    // 关闭连接并通知KeepAlive停止可用性检查
    this._keepAlive.stop();
    this._lwpChannel.close();
  }

  private async _heartbeat() {
    // 信息加密
    const heartBeatParams: ILwpChannel_Request = {
      lwp: '/!',
      headers: {},
    };
    return this._lwpChannel.send(heartBeatParams, {
      timeout: 5000, // 心跳请求超时时间5s
    });
  }

  private _bindInternalPushEventHandler() {
    // 收到登录态失效推送的处理函数
    const onLoginFailed = (reason: ELwpWebClient_EventType, request: ILwpChannel_Request) => {
      // 注意，需要等待返回信息上报成功后再关闭连接
      setTimeout(() => this._onLoginStatusExpired(reason, true));
      return Promise.resolve({
        code: ELwpChannel_ServerStatusCode.SUCCESS,
        headers: request.headers,
      });
    };

    // 监听服务端推送的退出登录事件
    this._lwpChannel.on(LwpWebClient.KICKOUT_CHANNEL, (request) => {
      return onLoginFailed(ELwpWebClient_EventType.AUTH_FAILED, request);
    });

    // 监听服务端推送的多端互踢事件
    this._lwpChannel.on(LwpWebClient.KICKOUT_CHANNELV2, (request) => {
      return onLoginFailed(ELwpWebClient_EventType.DEVICE_LIMIT, request);
    });

    // 监听服务端推送的连接数超限事件
    this._lwpChannel.on(LwpWebClient.SESSION_REMOVE, (request) => {
      return onLoginFailed(ELwpWebClient_EventType.CONNECT_LIMIT, request);
    });
  }

  // 触发LwpWebClient事件
  private _triggerEvent(level: ELwpChannel_EventLevel, type: ELwpWebClient_EventType, data?: any) {
    this._listener &&
      this._listener({
        level,
        type,
        data,
      });
  }
}
