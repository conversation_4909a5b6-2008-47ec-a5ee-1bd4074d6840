import CryptoJS from 'crypto-js';

export interface IEncryptWssMsg {
  ev: string; // 固定为 1
  data: string; // 加密后的数据 （随机向量 + 加密内容）
}

export class WssMsgEncryptUtilsClass {
  private isDevEnv: boolean = !/pre-/.test(location.hostname) && !/daily-/.test(location.hostname);
  private EV = '1';
  private clientPrivateKey: CryptoKey; // 客户端私钥
  private AES_KEY: ArrayBuffer; // 客户端私钥 + 服务端交换的公钥，得出的 aes密钥
  private aesDerivedKeyParams: AesDerivedKeyParams = {
    // 计算 AES 密钥配置
    name: 'AES-CBC',
    length: 128,
  };
  private ecKeyGenParams: EcKeyGenParams = {
    // 计算 ECDHKe 密钥配置
    name: 'ECDH',
    namedCurve: 'P-256',
  };

  isBrowserSupportEncrypt() {
    return !!crypto?.subtle && !!atob;
  }

  // 生成客户端，公钥和私钥
  async generateECDHKey(): Promise<string> {
    // 生成密钥对
    const keyPair = await crypto.subtle.generateKey(this.ecKeyGenParams, true, ['deriveKey']);

    // 导出公钥为Base64字符串
    const publicKey: ArrayBuffer = await crypto.subtle.exportKey('spki', keyPair.publicKey);
    // 将 二进制 转换为 base64数据，传给服务端
    const base64PublicKey = CryptoJS.enc.Base64.stringify(CryptoJS.lib.WordArray.create(publicKey));
    // 也可使用原生方法转换
    // const base64PublicKey = btoa(String.fromCharCode(...new Uint8Array(publicKey)));
    this.clientPrivateKey = keyPair.privateKey;
    return base64PublicKey;
  }
  /**
   * 根据 服务端公钥、客户端私钥，计算出 AES 密钥
   * @param serverPublicKey
   */
  async deriveAESKey(serverPublicKey: string) {
    const publicKey = await this.importServerPublicKey(serverPublicKey);
    const privateKey = this.clientPrivateKey;
    const derivedKey: CryptoKey = await crypto.subtle.deriveKey(
      {
        name: this.ecKeyGenParams.name,
        public: publicKey,
      },
      privateKey,
      this.aesDerivedKeyParams,
      true,
      ['encrypt', 'decrypt'],
    );

    const key = await crypto.subtle.exportKey('raw', derivedKey);
    this.isDevEnv &&
      console.log(
        '==== AES ====',
        'AES_KEY',
        CryptoJS.enc.Base64.stringify(CryptoJS.lib.WordArray.create(key)),
      );
    this.AES_KEY = key; // 二进制的 AES 密钥
  }
  /**
   * 加密消息
   * @param msg
   * @returns
   */
  encryptWssMsg(msg: Record<string, any>): IEncryptWssMsg {
    if (!this.AES_KEY || !msg) return msg as IEncryptWssMsg;
    this.isDevEnv &&
      console.log('==== AES ====', 'encryptWssMsg Start', {
        msg,
      });
    const startTime = Date.now();
    const iv = CryptoJS.lib.WordArray.random(16); // 初始化 16字节的 向量，用作数据传输的 padding
    const key = CryptoJS.lib.WordArray.create(this.AES_KEY); // 密钥从二进制转换为 WordArray
    const message = JSON.stringify(msg);
    // 使用 iv 和 key，进行加密
    const encrypted = CryptoJS.AES.encrypt(message, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    // 封装为 IEncryptWssMsg 类型
    const data = {
      ev: this.EV,
      data: CryptoJS.enc.Base64.stringify(iv.clone().concat(encrypted.ciphertext)),
    };
    this.isDevEnv &&
      console.log('==== AES ====', `encryptWssMsg cost: ${Date.now() - startTime}ms`, {
        msg,
        iv,
        text: encrypted.ciphertext,
      });
    return data;
  }
  /**
   * 检测是否为加密的消息体
   * @param msg
   * @returns
   */
  isEncryptedWssMsg(msg: Record<string, any>): boolean {
    return msg.ev === this.EV && typeof msg.data === 'string';
  }

  /**
   * 解密消息
   * @param msg
   * @returns
   */
  decryptWssMsg(msg: IEncryptWssMsg): Record<string, any> {
    if (this.isEncryptedWssMsg(msg)) {
      this.isDevEnv &&
        console.log('==== AES ====', 'decryptWssMsg Start', {
          msg,
        });
      const startTime = Date.now();
      const wordArrayMsg = CryptoJS.enc.Base64.parse(msg.data); // 将加密内容转换为 WordArray 类型
      const iv = this.sliceWordArray(wordArrayMsg, 0, 4); // 取出接入层设置的 向量
      const finalMsg = this.sliceWordArray(wordArrayMsg, 4, wordArrayMsg.words.length); // 取出数据内容
      const key = CryptoJS.lib.WordArray.create(this.AES_KEY); // 密钥 二进制 to WordArray
      // 通过 iv、key 进行解密
      const decrypted = CryptoJS.AES.decrypt(CryptoJS.enc.Base64.stringify(finalMsg), key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      try {
        const ret = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypted));
        this.isDevEnv &&
          console.log(`==== AES ==== decryptWssMsg, cost: ${Date.now() - startTime}ms`, {
            msg,
            iv,
            finalMsg,
            decrypted,
            ret: CryptoJS.enc.Utf8.stringify(decrypted),
          });
        return ret;
      } catch (ex) {
        // console
      }
    }
    return msg;
  }

  /**
   * 将交换得到的服务端 公钥，import，得到 CryptoKey 类型结果，以进行 deriveAESKey
   * @param base64PublicKey
   * @returns
   */
  private async importServerPublicKey(base64PublicKey: string): Promise<CryptoKey> {
    const publicKeyRaw = atob(base64PublicKey);
    const publicKeyBuffer = new Uint8Array(new ArrayBuffer(publicKeyRaw.length));

    for (let i = 0; i < publicKeyRaw.length; ++i) {
      publicKeyBuffer[i] = publicKeyRaw.charCodeAt(i);
    }
    return crypto.subtle.importKey('spki', publicKeyBuffer, this.ecKeyGenParams, true, []);
  }

  /**
   * 对 wordArray 切片
   * @param wordArray
   * @param start
   * @param end
   * @returns
   */
  private sliceWordArray(wordArray: CryptoJS.lib.WordArray, start: number, end: number) {
    // 克隆原始WordArray，以免修改原始数据
    const clone = wordArray.clone();
    // 截取指定范围的字数组
    clone.words = clone.words.slice(start, end);
    // 重新计算截取后的长度
    clone.sigBytes =
      (clone.words.length - 1) * 4 +
      Math.min(clone.words[clone.words.length - 1].toString(2).length, 4);
    return clone;
  }
}
