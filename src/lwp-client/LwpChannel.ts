// ****************************************************************************
// Lwp协议层：lwp协议通信通道，实现基于lwp协议的数据传输与返回消息解析
// ****************************************************************************

// ************************************
// 通用结构定义
// ************************************
import stringify from 'fast-json-stable-stringify';
import { IEncryptWssMsg, WssMsgEncryptUtilsClass } from './encrypt';
import { Signer } from './sign';
import {
  ELwpChannel_EventLevel,
  ELwpChannel_EventType,
  ELwpChannel_ServerStatusCode,
  ILwpChannel_Event,
  ILwpChannel_Request,
  ILwpChannel_RequestOption,
  ILwpChannel_Response,
  IMap,
  TLwpChannel_EventListener,
  TLwpChannel_RequestHandler,
} from './types';

// ************************************
// LwpChannel定义
// ************************************
export interface ILwpChannelProps {
  requestTimeout: number;
  aesMsgEncryption?: WssMsgEncryptUtilsClass | null;
  signer?: Signer | null;
}

export class LwpChannel {
  private readonly _requestTimeout: number;
  private readonly _aesMsgEncryption: WssMsgEncryptUtilsClass | null;
  private readonly _signer: Signer | null;

  // WebSocket实例
  private _webSocket: WebSocket | null = null;

  // 调用者注册的消息监听列表，监听服务端推送消息
  private _topicHandlers: IMap<TLwpChannel_RequestHandler> = {};

  // 用于生成随机mid
  private _sendMsgCount: number;

  // 已发送的消息列表，等待服务器返回
  private _pendingRequests: IMap<{
    request: ILwpChannel_Request; // 原始请求参数
    timeoutId: NodeJS.Timeout | number;
    resolve: (data: ILwpChannel_Response) => void;
    reject: (event: ILwpChannel_Event) => void;
  }> = {};

  constructor(props: ILwpChannelProps) {
    this._requestTimeout = props.requestTimeout;
    this._sendMsgCount = Math.floor(Math.random() * 1000000);
    this._aesMsgEncryption = props.aesMsgEncryption;
    this._signer = props.signer;
  }

  // 建立lwp通信通道
  // onClose方法，只有在已经连接成功的情况下，因自动或手动的原因断开连接，才会触发
  // 注意，如果在之前连接没有断开的情况下尝试连接，则不会触发上一个连接的onClose事件
  // 这是为了避免断网情况下由于缓冲区不为空无法及时关闭websocket，从而导致onClose事件触发不符合预期问题。
  // 同时也为了避免因多次重连而导致的回调重复触发问题
  connect(url: string, onClose: () => void): Promise<void> {
    return new Promise((resolve, reject) => {
      // reject所有pending消息
      this._cleanPendingRequests();

      if (this._webSocket) {
        // 关闭原本的webSocket连接，不触发原本websocket的onclose事件
        this._webSocket.onclose = null;
        this._webSocket.close();
      }

      const webSocket = new WebSocket(url);
      this._webSocket = webSocket;

      webSocket.onmessage = (msgEvent) => {
        this._onReceive(msgEvent.data);
      };

      webSocket.onerror = (e) => {
        // 清空所有pending消息
        this._cleanPendingRequests();
        this._webSocket = null;

        // 触发错误事件
        this._triggerEvent(ELwpChannel_EventLevel.ERROR, ELwpChannel_EventType.CONNECT_FAILED, e);

        reject(e);
      };

      webSocket.onopen = () => {
        webSocket.onclose = () => {
          // 清空所有pending消息
          this._cleanPendingRequests();
          this._webSocket = null;

          // 触发关闭事件
          this._triggerEvent(
            ELwpChannel_EventLevel.ERROR,
            ELwpChannel_EventType.CONNECT_CLOSED,
            url,
          );

          // 调用连接时设置的关闭回调
          onClose && onClose();
        };

        this._triggerEvent(ELwpChannel_EventLevel.INFO, ELwpChannel_EventType.CONNECT_SUCCESS, url);
        resolve();
      };
    });
  }

  // 如果当前已连接，则关闭当前连接，手动触发connect设置的onClose方法，因为onclose有可能会因为缓冲区不为空导致延迟触发
  close() {
    // 只有连接是开启状态时才主动触发onclose，因为如果连接是断开的，可能有两种情况：
    // 1、之前连接成功，现在已经断开了。那说明已经触发过onclose事件，不要重复触发
    // 2、之前没有连接成功，触发onclose可能会导致逻辑错误，这里不要触发
    if (this._webSocket && this._webSocket.readyState === WebSocket.OPEN) {
      const wsOnClose = this._webSocket.onclose;
      // 清空onclose方法，防止意外触发
      this._webSocket.onclose = null;
      this._webSocket.close();
      // 手动触发connect设置的onClose方法
      wsOnClose && wsOnClose.call(this);
    }
  }

  // 发送消息
  async send(
    request: ILwpChannel_Request,
    option?: ILwpChannel_RequestOption,
  ): Promise<ILwpChannel_Response> {
    // 生成随机msgId
    const msgId: string = this._genRandId();
    let doSendParams: IEncryptWssMsg | ILwpChannel_Request = {
      lwp: request.lwp,
      headers: {
        referer: location.href,
        ...request.headers,
        // 这种处理方式只是为了符合服务端的要求。
        // 服务端之前计划用这个mid字段来做RPC重发，后来并没有使用，所以这里就固定写成' 0'了。
        mid: `${msgId} 0`,
      },
      body: request.body,
    };

    if (this._signer) {
      try {
        doSendParams = await this._signer.signRequest(doSendParams);
      } catch (error) {
        console.error('signRequest error, please check your signer', error);
      }
    }

    if (this._aesMsgEncryption) {
      if (request['key-exchange']) {
        // 处理reg请求携带公钥
        doSendParams['key-exchange'] = request['key-exchange'];
      } else {
        // 信息加密
        doSendParams = this._aesMsgEncryption.encryptWssMsg(doSendParams);
      }
    }

    return new Promise((resolve, reject) => {
      this._doSend(doSendParams)
        .then(() => {
          // 如果消息发送成功，则注册pending列表，等待消息返回
          this._pendingRequests[msgId] = {
            request,
            resolve,
            reject,
            timeoutId: setTimeout(
              () => {
                // 若当前连接超时，则reject当前请求，并将其从pending列表中移除
                this._triggerEvent(
                  ELwpChannel_EventLevel.ERROR,
                  ELwpChannel_EventType.SEND_TIMEOUT,
                  request,
                );
                delete this._pendingRequests[msgId];
                reject(ELwpChannel_EventType.SEND_TIMEOUT);
              },
              (option && option.timeout) || this._requestTimeout,
            ),
          };
        })
        .catch(() => {
          reject(ELwpChannel_EventType.SEND_ERROR);
        });
    });
  }

  // 监听来自于服务端的推送消息。
  // 注意，一个topic只可以注册一个handler。
  // 因为要给服务端上报消息的处理状态，如果有多个handler，可能会导致返回状态混乱。
  on(topic: string, handler: TLwpChannel_RequestHandler) {
    this._topicHandlers[topic] = handler;
  }
  off(topic: string) {
    delete this._topicHandlers[topic];
  }

  // 设置LwpChannel事件监听器，一般用于通道异常处理或者打log
  listen(listener: TLwpChannel_EventListener) {
    this._listener = listener;
  }

  // 外部传入的事件监听方法，一般用于通道异常处理或者打log
  private _listener: TLwpChannel_EventListener = () => {};

  // 随机ID生成器
  private _genRandId(): string {
    const random: number = Math.floor(Math.random() * 1000000);
    const time: number = new Date().getTime();
    return `${random}${time}${this._sendMsgCount++}`;
  }

  // 触发LwpClient事件
  private _triggerEvent(level: ELwpChannel_EventLevel, type: ELwpChannel_EventType, data?: any) {
    this._listener &&
      this._listener({
        level,
        type,
        data,
      });
  }

  // 消息发送方法
  private _doSend(
    data: ILwpChannel_Request | ILwpChannel_Response | IEncryptWssMsg,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this._webSocket && this._webSocket.readyState === WebSocket.OPEN) {
        // 向webSocket发送消息
        this._triggerEvent(ELwpChannel_EventLevel.INFO, ELwpChannel_EventType.SEND_MSG, data);
        const dataStr = stringify(data);
        this._webSocket.send(dataStr);
        resolve();
      } else {
        // webSocket连接失效
        this._triggerEvent(ELwpChannel_EventLevel.ERROR, ELwpChannel_EventType.SEND_ERROR, data);
        reject();
      }
    });
  }

  // 消息处理函数
  private _onReceive(data: any) {
    // 消息类型可能是请求返回的消息或服务端主动推送的消息
    let msg: ILwpChannel_Request & ILwpChannel_Response & IEncryptWssMsg;

    try {
      msg = JSON.parse(data);
    } catch (e) {
      // 消息解析异常
      return;
    }

    this._triggerEvent(ELwpChannel_EventLevel.INFO, ELwpChannel_EventType.RECEIVE_MSG, msg);
    if (msg.data && this._aesMsgEncryption) {
      // 请求返回消息 数据格式是 IEncryptWssMsg
      try {
        // 解密字端
        msg = this._aesMsgEncryption.decryptWssMsg(msg) as ILwpChannel_Request &
        ILwpChannel_Response &
        IEncryptWssMsg;
      } catch (error) {}
    }

    if (this._signer && msg?.headers?.['server-timestamp']) {
      this._signer.setContext({
        'server-timestamp': msg.headers['server-timestamp'],
      });
    }

    if (msg.code && msg.headers) {
      const mid: string = (msg.headers.mid || '').split(' ')[0];
      const pendingItem = this._pendingRequests[mid];
      pendingItem && pendingItem.timeoutId && clearTimeout(pendingItem.timeoutId);
      pendingItem && pendingItem.resolve && pendingItem.resolve(msg);
      delete this._pendingRequests[mid];
    } else if (msg.lwp && msg.headers) {
      // 服务端主动推送消息
      const topicHandler = this._topicHandlers[msg.lwp];
      if (topicHandler) {
        topicHandler(msg).then((response) => {
          this._doSend(response).then(
            () => {},
            () => {},
          );
        });
      } else {
        // 未处理的消息，默认上报成功状态
        this._doSend({
          code: ELwpChannel_ServerStatusCode.SUCCESS,
          headers: msg.headers,
        }).then(
          () => {},
          () => {},
        );
      }
    } else {
      // 未解析消息类型，Do nothing
    }
  }

  // 清空pending消息
  private _cleanPendingRequests() {
    for (const mid in this._pendingRequests) {
      if (this._pendingRequests[mid]) {
        const pendingItem = this._pendingRequests[mid];
        pendingItem.timeoutId && clearTimeout(pendingItem.timeoutId);
        pendingItem.reject &&
          pendingItem.reject({
            level: ELwpChannel_EventLevel.ERROR,
            type: ELwpChannel_EventType.SEND_ERROR,
            data: pendingItem.request,
          });
      }
    }
    // 清空pending列表
    this._pendingRequests = {};
  }
}
