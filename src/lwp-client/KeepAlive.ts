// ****************************************************************************
// 长连接保活：提供通用的心跳机制与连接状态检查机制
// ****************************************************************************

interface IKeepAliveParams {
  maxConnectCount: number; // 重连尝试最大次数
  connectCountIncInterval: number; // 连接次数自增时间间隔
  heartbeatInterval: number; // 心跳时间间隔
  connect: (onClose: () => void) => Promise<void>; // 建连方法
  update: () => Promise<any>; // 心跳方法
  onError: () => void; // 连接失败的回调，注意，该方法可能是WaitAlive失败主动触发，也可能是心跳请求失败被动触发
}

export class KeepAlive {
  private readonly _maxConnectCount: number;
  private readonly _connectCountIncInterval: number;
  private readonly _heartbeatInterval: number;
  private readonly _connect: (onClose: () => void) => Promise<void>;
  private readonly _update: () => Promise<void>;
  private readonly _onError: () => void;

  // 可用连接次数
  private _isConnecting = false; // 是否正在尝试建连
  private _connectCountLeft = 0;
  private _connectCountIncIId: NodeJS.Timeout | number;
  private _isErrorTriggered: boolean;
  // 心跳
  private _heartbeatIId: NodeJS.Timeout | number;

  // 连接状态检查
  private _isAlive = false;
  private _alivePromise: Promise<void> | null;
  private _pendingAliveResolve: (() => void) | null;
  private _pendingAliveReject: (() => void) | null;

  constructor(params: IKeepAliveParams) {
    this._maxConnectCount = params.maxConnectCount;
    this._connectCountIncInterval = params.connectCountIncInterval;
    this._heartbeatInterval = params.heartbeatInterval;
    this._connect = params.connect;
    this._update = params.update;
    this._onError = params.onError;
  }

  start() {
    this._isAlive = false;
    this._isConnecting = false;
    this._connectCountLeft = this._maxConnectCount;
    clearInterval(this._connectCountIncIId);
    clearInterval(this._heartbeatIId);
    this._isErrorTriggered = false;
  }

  stop() {
    this._isAlive = false;
    this._isConnecting = false;
    this._connectCountLeft = 0;
    clearInterval(this._connectCountIncIId);
    clearInterval(this._heartbeatIId);
    this._handleAlivePromise(false);
    this._triggerError();
  }

  // 等待连接活跃
  waitAlive(): Promise<void> {
    if (!this._alivePromise) {
      this._alivePromise = new Promise((resolve, reject) => {
        this._pendingAliveResolve = resolve;
        this._pendingAliveReject = reject;

        clearInterval(this._heartbeatIId);

        if (this._isAlive) {
          // 如果已连接，则发送心跳请求，确认连接状态
          this._update()
            .then(() => {
              // 心跳请求成功，设置连接状态并触发alivePromise回调
              this._isAlive = true;
              this._handleAlivePromise(true);
              this._restartHeartbeat();
            })
            .catch(() => {
              // 心跳请求失败，连接状态失效，尝试重连。
              // 连接成功或失败会触发对应pendingPromise
              this._isAlive = false;
              this._tryConnect();
            });
        } else {
          // 如果当前未连接则发起连接，等待连接成功。
          // 连接成功或失败会触发对应pendingPromise
          this._tryConnect();
        }
      });
    }
    return this._alivePromise;
  }

  // 尝试建立连接
  private _tryConnect() {
    if (!this._isConnecting) {
      this._isAlive = false;

      clearInterval(this._connectCountIncIId);
      clearInterval(this._heartbeatIId);

      if (this._connectCountLeft > 0) {
        --this._connectCountLeft;
        this._isConnecting = true;

        this._connect(() => {
          // 连接断开回调，尝试重连
          this._isAlive = false;
          this._isConnecting = false;
          this._tryConnect();
        })
          .then(() => {
            // 连接成功
            this._isAlive = true;
            this._isConnecting = false;
            this._handleAlivePromise(true);
            this._restartAutoCountInc();
            this._restartHeartbeat();
          })
          .catch(() => {
            // 连接失败，尝试重连
            this._isAlive = false;
            this._isConnecting = false;
            this._tryConnect();
          });
      } else {
        // 尝试连接次数超过上限，连接失败
        this._handleAlivePromise(false);
        this._triggerError();
      }
    }
  }

  private _handleAlivePromise(isActive: boolean) {
    // 新宏任务，延迟处理promise，以确保promise回调可以正确触发
    setTimeout(() => {
      if (isActive) {
        this._pendingAliveResolve && this._pendingAliveResolve();
      } else {
        this._pendingAliveReject && this._pendingAliveReject();
      }
      this._pendingAliveResolve = null;
      this._pendingAliveReject = null;
      this._alivePromise = null;
    });
  }

  private _triggerError() {
    if (!this._isErrorTriggered) {
      this._isErrorTriggered = true;
      this._onError && this._onError();
    }
  }

  private _restartHeartbeat() {
    clearInterval(this._heartbeatIId);
    this._heartbeatIId = setInterval(() => {
      this.waitAlive().then(
        () => {},
        () => {},
      );
    }, this._heartbeatInterval);
  }

  private _restartAutoCountInc() {
    // 可用连接次数每 CONNECT_COUNT_INC_INTERVAL 自增 1
    clearInterval(this._connectCountIncIId);
    this._connectCountIncIId = setInterval(() => {
      this._connectCountLeft =
        this._connectCountLeft >= this._maxConnectCount
          ? this._maxConnectCount
          : this._connectCountLeft + 1;
    }, this._connectCountIncInterval);
  }
}
