export interface LwpRegAuthInfo {
  token?: string;
  syncHeader?: string;
  syncContext?: string;
  syncDeviceLabel?: string;
  deviceId?: string;
}

export interface IMap<T> {
  [key: string]: T;
}

// ************************************
// 请求发送相关结构定义
// ************************************

// lwp协议请求结构
export interface ILwpChannel_Request {
  lwp: string;
  headers: IMap<any>;
  body?: any;
  'key-exchange'?: string;
}

// lwp请求发送时的可选参数定义
export interface ILwpChannel_RequestOption {
  timeout?: number; // 请求超时毫秒数
}

// ************************************
// 请求响应相关结构定义
// ************************************

// lwp协议响应结构
export interface ILwpChannel_Response {
  code: ELwpChannel_ServerStatusCode;
  headers: IMap<any>;
  body?: any;
}

// lwp协议服务端响应状态码
export enum ELwpChannel_ServerStatusCode {
  SUCCESS = 200, // 成功 200
  NOT_FOUND = 400, // 接口不存在（404）
  NOT_AUTHED = 401, // 接口需要登录态（401）
  TIMEOUT = 408, // 接口调用超时（408）
  SERVER_ERROR = 500, // 服务端错误（500）
}

// 接收服务端推送消息的处理函数结构定义
export type TLwpChannel_RequestHandler = (
  request: ILwpChannel_Request,
) => Promise<ILwpChannel_Response>;

// ************************************
// LwpChannel事件相关结构定义，用于事件监听器
// ************************************

// LwpChannel事件级别
export enum ELwpChannel_EventLevel {
  INFO = 'INFO', // 当前通道发生常规事件时触发，包括连接成功或收发消息
  ERROR = 'ERROR', // 当前通道发生异常时触发，表示当前连接状态异常，需要重连
}

// LwpChannel事件所属分组
export enum ELwpChannel_EventType {
  // WebSocket相关事件
  CONNECT_SUCCESS = 'CONNECT_SUCCESS', // INFO WebSocket建立连接成功
  CONNECT_CLOSED = 'CONNECT_CLOSED', // ERROR WebSocket连接关闭（可能是主动的断开也可能是被动的断开）
  CONNECT_FAILED = 'CONNECT_FAILED', // ERROR WebSocket建立连接失败
  // 收发消息相关event
  SEND_MSG = 'SEND_MSG', // INFO 发送消息成功
  RECEIVE_MSG = 'RECEIVE_MSG', // INFO 收到消息
  SEND_TIMEOUT = 'SEND_TIMEOUT', // ERROR 消息发送超时
  SEND_ERROR = 'SEND_ERROR', // ERROR 消息发送失败，可以在这里处理消息重发机制
}

// LwpChannel事件结构
export interface ILwpChannel_Event {
  level: ELwpChannel_EventLevel;
  type: ELwpChannel_EventType;
  data?: any;
}

// LwpChannel事件监听函数结构定义
export type TLwpChannel_EventListener = (event: ILwpChannel_Event) => void;
