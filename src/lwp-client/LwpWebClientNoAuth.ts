import { KeepAlive } from './KeepAlive';
import { LwpChannel } from './LwpChannel';
import {
  ELwpChannel_EventType,
  ILwpChannel_Request,
  ILwpChannel_RequestOption,
  ILwpChannel_Response,
  TLwpChannel_EventListener,
  TLwpChannel_RequestHandler,
} from './types';

// ************************************
// LwpWebClientNoAuth初始化参数
// ************************************

export interface ILwpWebClientNoAuthProps {
  wsUrl: string;
}

export class LwpWebClientNoAuth {
  private readonly _wsUrl: string;

  // lwpChannel实例
  private readonly _lwpChannel: LwpChannel;
  // KeepAlive实例
  private readonly _keepAlive: KeepAlive;

  private _onError: () => void;

  constructor(props: ILwpWebClientNoAuthProps) {
    this._wsUrl = props.wsUrl;

    this._lwpChannel = new LwpChannel({
      requestTimeout: 10000,
    });

    this._keepAlive = new KeepAlive({
      maxConnectCount: 3,
      connectCountIncInterval: 30000,
      heartbeatInterval: 10000,
      connect: (onClose) => {
        return this._lwpChannel.connect(this._wsUrl, onClose);
      },
      update: () => {
        return this._heartbeat();
      },
      onError: () => {
        this._onError && this._onError();
      },
    });
  }

  // 建立连接并鉴权
  connect(onError: () => void) {
    this._onError = onError;
    this._keepAlive.start();
    return this._keepAlive.waitAlive();
  }

  // 发送消息
  async sendMsg(
    request: ILwpChannel_Request,
    option?: ILwpChannel_RequestOption,
  ): Promise<ILwpChannel_Response> {
    try {
      await this._keepAlive.waitAlive();
      return this._lwpChannel.send(request, option);
    } catch (e) {
      return Promise.reject(ELwpChannel_EventType.SEND_ERROR);
    }
  }

  // 监听来自于服务端的推送消息。
  // 注意，一个topic只可以注册一个handler。
  // 因为要给服务端上报消息的处理状态，如果有多个handler，可能会导致返回状态混乱。
  on(topic: string, handler: TLwpChannel_RequestHandler) {
    this._lwpChannel.on(topic, handler);
  }

  off(topic: string) {
    this._lwpChannel.off(topic);
  }

  // 设置Client事件监听器，主要用于监听连接失败或授权失败的client事件和消息收发事件
  // 注意！！！listener只可以设置一个，后面设置的会覆盖前面的。
  listen(listener: TLwpChannel_EventListener) {
    this._lwpChannel.listen(listener);
  }

  private async _heartbeat() {
    return this._lwpChannel.send(
      {
        lwp: '/!',
        headers: {},
      },
      {
        timeout: 5000,
      },
    );
  }
}
