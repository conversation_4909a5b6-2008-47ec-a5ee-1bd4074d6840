import { useState, useCallback, useRef, useEffect } from 'react'
import { i18next } from '@ali/dingtalk-i18n'
import { getQuotaUsage, QuotaUsageResponse, QuotaUsageTotalResponse } from '@/apis/quota'
import { log } from '@/utils/console'

interface UseQuotaUsageReturn {
  quotaUsage: QuotaUsageResponse | null
  loading: boolean
  error: string | null
  refreshQuotaUsage: () => Promise<void>
  updateUsedQuota: (incrementBy: number) => void
}

/**
 * Custom hook for managing quota usage data
 * Provides centralized quota management with automatic refresh and manual update capabilities
 */
const useQuotaUsage = (resourceKey: string = 'ai_material'): UseQuotaUsageReturn => {
  const [quotaUsage, setQuotaUsage] = useState<QuotaUsageResponse | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const isMountedRef = useRef<boolean>(true)

  // Fetch quota usage data from API
  const fetchQuotaUsage = useCallback(async (): Promise<void> => {
    if (!isMountedRef.current) return

    setLoading(true)
    setError(null)

    try {
      const response = (await getQuotaUsage({
        resourceKey,
      })) as QuotaUsageTotalResponse

      if (!isMountedRef.current) return

      if (response.success && response.data) {
        setQuotaUsage(response.data)
      } else {
        const errorMessage =
          response.errorMsg ||
          i18next.t('j-dingtalk-web_hooks_useQuotaUsage_FailedToObtainQuotaUsage')
        setError(errorMessage)
        log.error(errorMessage)
      }
    } catch (err) {
      if (!isMountedRef.current) return

      const errorMessage =
        err instanceof Error
          ? err.message
          : i18next.t('j-dingtalk-web_hooks_useQuotaUsage_FailedToObtainQuotaUsage')
      setError(errorMessage)
      log.error('Failed to fetch quota usage:', err)
    } finally {
      if (isMountedRef.current) {
        setLoading(false)
      }
    }
  }, [resourceKey])

  // Public method to refresh quota usage
  const refreshQuotaUsage = useCallback(async (): Promise<void> => {
    await fetchQuotaUsage()
  }, [fetchQuotaUsage])

  // Update used quota locally (for immediate UI feedback)
  const updateUsedQuota = useCallback((incrementBy: number): void => {
    setQuotaUsage((prev) => {
      if (!prev) return null

      const currentUsedQuota = prev.usedQuota || 0
      const currentTotalQuota = prev.totalQuota || 0
      const newUsedQuota = Math.max(0, currentUsedQuota + incrementBy)
      // Ensure usedQuota doesn't exceed totalQuota
      const finalUsedQuota = Math.min(newUsedQuota, currentTotalQuota)

      return {
        ...prev,
        usedQuota: finalUsedQuota,
      }
    })
  }, [])

  // Initial fetch on mount
  useEffect(() => {
    fetchQuotaUsage()

    // Cleanup function
    return () => {
      isMountedRef.current = false
    }
  }, [fetchQuotaUsage])

  return {
    quotaUsage,
    loading,
    error,
    refreshQuotaUsage,
    updateUsedQuota,
  }
}

export default useQuotaUsage
