import { useState, useEffect, useRef } from 'react'

interface UseOrderCountdownOptions {
  gmtCreate: Date
  onCountdownEnd?: () => void
  enabled?: boolean // Whether to enable countdown
}

interface CountdownResult {
  timeRemaining: string
  isExpired: boolean
}

/**
 * Custom hook for order countdown timer
 * @param gmtCreate - Order creation time
 * @param onCountdownEnd - Callback when countdown reaches zero
 * @param enabled - Whether to enable countdown (default: true)
 * @returns Formatted countdown string and expiry status
 */
export const useOrderCountdown = ({
  gmtCreate,
  onCountdownEnd,
  enabled = true
}: UseOrderCountdownOptions): CountdownResult => {
  const [timeRemaining, setTimeRemaining] = useState<string>('(24:00:00)')
  const [isExpired, setIsExpired] = useState(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const onCountdownEndRef = useRef(onCountdownEnd)

  // Update callback ref
  useEffect(() => {
    onCountdownEndRef.current = onCountdownEnd
  }, [onCountdownEnd])

  useEffect(() => {
    // If countdown is disabled, don't start timer
    if (!enabled) {
      setTimeRemaining('(24:00:00)')
      setIsExpired(false)
      return
    }

    const calculateTimeRemaining = () => {
      const now = new Date()
      const createTime = new Date(gmtCreate)
      const expiryTime = new Date(createTime.getTime() + 24 * 60 * 60 * 1000) // 24 hours from creation
      const timeDiff = expiryTime.getTime() - now.getTime()

      if (timeDiff <= 0) {
        // Timer expired
        setTimeRemaining('(00:00:00)')
        setIsExpired(true)

        // Clear timer
        if (timerRef.current) {
          clearInterval(timerRef.current)
          timerRef.current = null
        }

        // Trigger callback
        if (onCountdownEndRef.current) {
          onCountdownEndRef.current()
        }

        return
      }

      // Calculate hours, minutes, seconds
      const hours = Math.floor(timeDiff / (1000 * 60 * 60))
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)

      // Format as "(HH:MM:SS)"
      const formattedTime = `(${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}:${seconds.toString().padStart(2, '0')})`

      setTimeRemaining(formattedTime)
      setIsExpired(false)
    }

    // Calculate initial time
    calculateTimeRemaining()

    // Set up interval if not expired
    if (!isExpired) {
      timerRef.current = setInterval(calculateTimeRemaining, 1000)
    }

    // Cleanup function
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [gmtCreate, isExpired, enabled])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [])

  return {
    timeRemaining,
    isExpired,
  }
}
