import { useState } from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { Toast } from 'dingtalk-design-mobile';
import { isMobileDevice } from '@/utils/jsapi';
import { sendUT } from '@/utils/trace';
import {
  checkFreeQuotaAccess,
  grantFreeQuota,
  FreeQuotaAccessResponse,
  QuotaAccessResponse,
} from '@/apis/quota';

interface UseFreeQuotaOptions {
  resourceKey: string; // 资源键，例如 'ai_material'
  eventPrefix: string; // 埋点事件前缀，例如 'aigc_video'
  errorMessages: {
    obtainFailed: string; // 获取免费额度失败的提示
    interfaceAbnormal: string; // 接口异常的提示
    collectFailed: string; // 领取失败的提示
  };
}

interface UseFreeQuotaReturn {
  freeModalVisible: boolean;
  quotaAccessData: FreeQuotaAccessResponse | null;
  freeModalLoading: boolean;
  checkFreeQuota: () => Promise<void>;
  handleFreeModalClose: () => void;
  handleFreeModalReceive: (refreshQuotaUsage: () => void) => Promise<void>;
}

/**
 * Free quota management hook
 * @param options Configuration options including resource key and event prefix
 * @returns Modal state and handlers for free quota operations
 */
export const useFreeQuota = (options: UseFreeQuotaOptions): UseFreeQuotaReturn => {
  const [freeModalVisible, setFreeModalVisible] = useState(false);
  const [quotaAccessData, setQuotaAccessData] = useState<FreeQuotaAccessResponse | null>(null);
  const [freeModalLoading, setFreeModalLoading] = useState(false);

  const { resourceKey, eventPrefix, errorMessages } = options;

  // Check free quota access
  const checkFreeQuota = async () => {
    try {
      const response = await checkFreeQuotaAccess({
        resourceKey,
        // mockSystem: 'y',
        // mockAdmin: 'y',
      }) as QuotaAccessResponse;

      if (response.success) {
        if (response?.data?.result) {
          setQuotaAccessData(response?.data);
          setFreeModalVisible(true);
        }
      } else {
        Toast.fail({
          content: response?.errorMsg || errorMessages.obtainFailed,
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
      }
    } catch (error) {
      // Handle error silently, don't show modal if API fails
      Toast.fail({
        content: errorMessages.interfaceAbnormal,
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Handle free modal close
  const handleFreeModalClose = () => {
    setFreeModalVisible(false);
  };

  // Handle free modal receive
  const handleFreeModalReceive = async (refreshQuotaUsage: () => void) => {
    setFreeModalLoading(true);

    // Send tracking event
    sendUT(`${eventPrefix}_free_modal_receive`, {
      device: isMobileDevice() ? 'mobile' : 'pc',
      source: quotaAccessData?.source || '',
      itemCode: quotaAccessData?.itemCode || '',
    });

    try {
      // Call free quota grant API
      const response = await grantFreeQuota({
        itemCode: quotaAccessData?.itemCode || '',
        resourceKey,
      }) as any;

      if (response.success) {
        // Show success message
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_ai-studio_ReceiveSuccess'),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
        // Refresh quota usage after successful grant
        refreshQuotaUsage();
      } else {
        // Show failure message
        Toast.fail({
          content: response.errorMsg || errorMessages.collectFailed,
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
      }
      // Close modal after successful grant
      setFreeModalVisible(false);
    } catch (error) {
      // Show failure message
      Toast.fail({
        content: errorMessages.collectFailed,
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    } finally {
      setFreeModalLoading(false);
    }
  };

  return {
    freeModalVisible,
    quotaAccessData,
    freeModalLoading,
    checkFreeQuota,
    handleFreeModalClose,
    handleFreeModalReceive,
  };
};

export default useFreeQuota;
