import { Toast } from 'dingtalk-design-mobile';
import { i18next } from '@ali/dingtalk-i18n';

/**
 * Copy text to clipboard with success/failure feedback
 * @param text - Text to copy
 * @param successMessage - Success message to display (optional)
 * @param failureMessage - Failure message to display (optional)
 * @returns Promise<boolean> - Returns true if copy was successful
 */
export const copyToClipboard = async (
  text: string,
  successMessage?: string,
  failureMessage?: string,
): Promise<boolean> => {
  const defaultSuccessMessage = i18next.t('j-dingtalk-web_pages_order-list_CopySuccess', {
    label: i18next.t('j-dingtalk-web_pages_order-list_Content') || '内容',
  });
  const defaultFailureMessage = i18next.t('j-dingtalk-web_pages_order-list_CopyFailed');

  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      // Modern clipboard API
      await navigator.clipboard.writeText(text);
      Toast.success({
        content: successMessage || defaultSuccessMessage,
        position: 'top',
        duration: 2,
      });
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        Toast.success({
          content: successMessage || defaultSuccessMessage,
          position: 'top',
          duration: 2,
        });
        return true;
      } else {
        throw new Error('Copy command failed');
      }
    }
  } catch (error) {
    Toast.fail({
      content: failureMessage || defaultFailureMessage,
      position: 'top',
      duration: 2,
    });
    return false;
  }
};

/**
 * Copy text to clipboard with custom label for success message
 * @param text - Text to copy
 * @param label - Label to display in success message
 * @returns Promise<boolean> - Returns true if copy was successful
 */
export const copyWithLabel = async (
  text: string,
  label: string,
): Promise<boolean> => {
  const successMessage = i18next.t('j-dingtalk-web_pages_order-list_CopySuccess', { label });
  return copyToClipboard(text, successMessage);
};
