/**
 * JSON parsing utilities and format conversion utilities
 */

/**
 * Safely parse JSON string with fallback value
 * @param jsonString - The JSON string to parse
 * @param fallback - Fallback value if parsing fails (default: null)
 * @returns Parsed object or fallback value
 */
export function safeJsonParse<T = any>(
  jsonString: string | null | undefined,
  fallback: T = null as T,
): T {
  if (!jsonString || typeof jsonString !== 'string') {
    return fallback;
  }

  try {
    const parsed = JSON.parse(jsonString);
    return parsed !== null && parsed !== undefined ? parsed : fallback;
  } catch (error) {
    // Silent fail, return fallback value
    return fallback;
  }
}

/**
 * Safely parse JSON string to array
 * @param jsonString - The JSON string to parse
 * @param fallback - Fallback array if parsing fails (default: [])
 * @returns Parsed array or fallback array
 */
export function safeJsonParseArray<T = any>(
  jsonString: string | null | undefined,
  fallback: T[] = [],
): T[] {
  const result = safeJsonParse(jsonString, fallback);
  return Array.isArray(result) ? result : fallback;
}

/**
 * Safely parse JSON string to object
 * @param jsonString - The JSON string to parse
 * @param fallback - Fallback object if parsing fails (default: {})
 * @returns Parsed object or fallback object
 */
export function safeJsonParseObject<T = Record<string, any>>(
  jsonString: string | null | undefined,
  fallback?: T,
): T {
  // Use Object.create(null) to create a clean object, then cast to T
  const defaultFallback = Object.assign(Object.create(null), {}) as T;
  const actualFallback = fallback ?? defaultFallback;
  const result = safeJsonParse(jsonString, actualFallback);
  const isValidObject = result && typeof result === 'object' && !Array.isArray(result);
  return isValidObject ? result : actualFallback;
}

/**
 * Convert timestamp to ISO string format
 * @param timestamp - Timestamp number or ISO string
 * @param fallback - Fallback value if conversion fails (default: empty string)
 * @returns ISO string or fallback value
 */
export function formatTimestampToISO(
  timestamp: number | string | null | undefined,
  fallback = '',
): string {
  if (!timestamp) {
    return fallback;
  }

  try {
    // If already a string, return as-is (assume it's already ISO format)
    if (typeof timestamp === 'string') {
      return timestamp;
    }

    // Convert number timestamp to ISO string
    if (typeof timestamp === 'number') {
      return new Date(timestamp).toISOString();
    }

    return fallback;
  } catch (error) {
    return fallback;
  }
}
