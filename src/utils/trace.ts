import UT from '@ali/dingtalk-jsapi/api/biz/util/ut';
import { log } from '@/utils/console';
import { isDingTalk, isMobileDevice } from '@/utils/jsapi';
// import { uploadUserTrack } from '@/apis';

const PageName = 'J_h5_page';

/**
 * 发送埋点
 * @param key 埋点key
 * @param value 埋点value
 */
export function sendUT(key?: string, value: any = {}) {
  const _key = key ? `${PageName}_${key}` : PageName;
  log('sendUT: ', _key, value);

  // 只在钉钉端内统计
  if (!isDingTalk()) {
    return;
  }

  UT({
    key: _key,
    value,
    // @ts-ignore - Dingtalk JSAPI typing issue
    ddWebTrack: true,
  });

  // uploadUserTrack({
  //   action: key,
  //   detail: JSON.stringify(value),
  //   categoryPath: PageName,
  // });
}

/**
 * Page usage time tracker interface
 */
export interface PageTimeTracker {
  cleanup: () => void;
}

/**
 * Initialize page usage time tracking
 * @param utKey - The UT tracking key for reporting usage time
 * @returns PageTimeTracker object with cleanup method
 */
export function initPageTimeTracking(utKey: string): PageTimeTracker {
  const totalVisibleTimeRef = { current: 0 }; // Total visible time in milliseconds
  const lastVisibleStartRef = { current: Date.now() }; // Last time page became visible

  // Handle page visibility changes for accurate time tracking
  const handleVisibilityChange = () => {
    const now = Date.now();
    if (document.hidden) {
      // Page became hidden, accumulate visible time
      totalVisibleTimeRef.current += now - lastVisibleStartRef.current;
    } else {
      // Page became visible, record start time
      lastVisibleStartRef.current = now;
    }
  };

  // Add visibility change listener
  document.addEventListener('visibilitychange', handleVisibilityChange);

  // Return cleanup function
  return {
    cleanup: () => {
      // Remove event listener
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      // Calculate final visible time if page is still visible
      if (!document.hidden) {
        totalVisibleTimeRef.current += Date.now() - lastVisibleStartRef.current;
      }

      // Calculate and report page usage time
      const usageTimeInSeconds = Math.round(totalVisibleTimeRef.current / 1000);

      sendUT(utKey, {
        device: isMobileDevice() ? 'mobile' : 'pc',
        duration: usageTimeInSeconds,
      });
    },
  };
}
