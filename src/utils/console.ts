/**
 * @desc Check if debug mode is enabled
 */
const isDebugMode = window.location.href?.indexOf('debug_mode=true') > -1;

/**
 * @desc Console logging utility type
 */
type LogLevel = 'log' | 'error' | 'warn' | 'info';

/**
 * @desc Enhanced logging function interface
 */
interface LogFunction {
  (...args: unknown[]): void;
  error: (...args: unknown[]) => void;
  warn: (...args: unknown[]) => void;
  info: (...args: unknown[]) => void;
  alert: (...args: unknown[]) => void;
}

/**
 * @desc Create a logging wrapper function
 * @param level The console method to use
 */
const createLogger = (level: LogLevel) => {
  return (...args: unknown[]): void => {
    if (!isDebugMode) {
      return;
    }
    // eslint-disable-next-line no-console
    console[level](...args);
  };
};

/**
 * @desc AI debugging utility function - only outputs logs when URL contains debug parameter
 * <AUTHOR>
 * @param {...unknown[]} args Parameters to print
 */
export const log: LogFunction = Object.assign(
  createLogger('log'),
  {
    error: createLogger('error'),
    warn: createLogger('warn'),
    info: createLogger('info'),
    alert: (...args: unknown[]): void => {
      if (!isDebugMode) {
        return;
      }

      try {
        // Format arguments for alert display
        const message = args.map((arg) => {
          if (typeof arg === 'object' && arg !== null) {
            return JSON.stringify(arg, null, 2);
          }
          return String(arg);
        }).join(' ');

        // eslint-disable-next-line no-alert
        window.alert(message);
      } catch (error) {
        // eslint-disable-next-line no-alert
        window.alert(args.join(' '));
      }
    },
  },
);
