import request from './base';

// 抠图请求参数接口 (Extract image request parameters interface)
export interface ExtractImageRequest {
  imgUrl: string; // 原图片URL (Original image URL)
}

// 抠图响应结果接口 (Extract image response interface)
export interface ExtractImageResponse {
  imgUrl: string; // 处理后的图片URL，成功时为抠图结果，失败时应为原图URL (Processed image URL)
  success: boolean; // 抠图是否成功 (Whether extraction was successful)
  errorMsg: string; // 错误信息，成功时可为空 (Error message, can be empty on success)
}

// Generate image request parameters interface
export interface GenerateImageRequest {
  imgUrl?: string; // Uploaded image URL for img2img
  requirements: string; // Text prompt requirements
  imgPixel: string; // Image pixel size (e.g., "800x800", "750x1000", "1280x720")
  isSegImage?: 'y' | 'n'; // Whether to use segmented image URL ('y') or original image URL ('n')
  [key: string]: any; // Allow additional parameters
}

// Check image status request parameters interface
export interface CheckImageStatusRequest {
  uuid: string; // Image generation task UUID
}

// List images request parameters interface
export interface ListImagesRequest {
  limit: number; // Number of images to fetch
  nextUuid?: string; // UUID for pagination
  [key: string]: any; // Allow additional parameters
}

// Generate HD image request parameters interface
export interface GenerateHDImageRequest {
  uuid: string; // Original image UUID
  [key: string]: any; // Allow additional parameters
}

// Remove image request parameters interface
export interface RemoveImageRequest {
  uuid: string; // Image UUID to remove
}

// Regenerate image request parameters interface
export interface ReGenerateImageRequest {
  fromUuid: string; // Original image UUID
  requirements?: string; // Updated text prompt
  [key: string]: any; // Allow additional parameters
}

// Rate image request parameters interface
export interface RateImageRequest {
  uuid: string; // Image UUID to rate
  rating: -1 | 0 | 1; // Rating value
}

// 抠图服务 (Image extraction service)
export const extractImage = (data: ExtractImageRequest) => {
  return request('/r/Adaptor/MaterialRpcI/extractImage', [data]);
};

// Generate image (生成图片)
export const generateImage = (data: GenerateImageRequest) => {
  return request('/r/Adaptor/MaterialRpcI/generateImage', [data]);
};

// Check image generation status (查看图片生成状态)
export const checkImageStatus = (data: CheckImageStatusRequest) => {
  return request('/r/Adaptor/MaterialRpcI/checkImageStatus', [data]);
};

// Generate HD image (生成高清大图)
export const generateHDImage = (data: GenerateHDImageRequest) => {
  return request('/r/Adaptor/MaterialRpcI/generateHDImage', [data]);
};

// Get image list (获取图片列表)
export const listImages = (data: ListImagesRequest) => {
  return request('/r/Adaptor/MaterialRpcI/listImages', [data]);
};

// Delete image (删除图片)
export const removeImage = (data: RemoveImageRequest) => {
  return request('/r/Adaptor/MaterialRpcI/removeImage', [data]);
};

// Regenerate image (重新生成图片)
export const reGenerateImage = (data: ReGenerateImageRequest) => {
  return request('/r/Adaptor/MaterialRpcI/reGenerateImage', [data]);
};

// Rate image (评价图片)
export const rateImage = (data: RateImageRequest) => {
  return request('/r/Adaptor/MaterialRpcI/rateImage', [data]);
};
