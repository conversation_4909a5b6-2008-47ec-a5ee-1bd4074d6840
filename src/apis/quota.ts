import request from './base';

export interface ResourceQuotaRequest {
  resourceKey: string;
  mockSystem?: string;
  mockAdmin?: string;
}

export interface QuotaAccessResponse {
  success: boolean;
  data: FreeQuotaAccessResponse;
  errorMsg: string;
}

export interface QuotaUsageRequest {
  resourceKey: string;
}

export interface QuotaUsageTotalResponse {
  success: boolean;
  data: QuotaUsageResponse;
  errorMsg: string;
}

export interface QuotaUsageResponse {
  totalQuota: number;
  usedQuota: number;
  uuid: string;
}

export interface FreeQuotaAccessResponse {
  result: boolean;
  source: 'system' | 'manual'; // system 系统推送 ; manual 人工推送
  itemCode: string;
}

export interface FreeQuotaGrantRequest {
  itemCode: string;
  resourceKey: string;
}

export interface FreeQuotaGrantResponse {
  success: boolean;
  errorMsg: string;
}

export interface CreateResourceOrderRequest {
  itemCode: string;
  resourceKey: string;
}

export interface UpdateResourceOrderRequest {
  orderUuid: string;
  data: string;
}

export interface UpdateResourceOrderResponse {
  success: boolean;
  errorMsg: string;
}

export interface GetResourceOrderRequest {
  orderUuid: string;
}

export interface PageResourceOrderRequest {
  resourceKey: string;
  currentPage: number;
  pageSize: number;
}

export interface CancelBenefitOrderRequest {
  orderUuid: string;
}

export interface CancelBenefitOrderResponse {
  success: boolean;
  errorMsg: string;
}

export interface CheckRejectedOrderRequest {
  resourceKey: string;
}

export interface CheckRejectedOrderResponse {
  success: boolean;
  hasRejectedOrder: boolean;
  errorMsg: string;
}

// Check free quota access
export const checkFreeQuotaAccess = (data: ResourceQuotaRequest) => {
  return request('/r/Adaptor/BenefitRpcI/checkFreeQuotaAccess', [data]);
};

// Grant free quota
export const grantFreeQuota = (data: FreeQuotaGrantRequest) => {
  return request('/r/Adaptor/BenefitRpcI/grantFreeQuota', [data]);
};

// Get quota usage
export const getQuotaUsage = (data: QuotaUsageRequest) => {
  return request('/r/Adaptor/BenefitRpcI/getQuotaUsage', [data]);
};

// Create order
export const createOrder = (data: CreateResourceOrderRequest) => {
  return request('/r/Adaptor/BenefitRpcI/createOrder', [data]);
};

// Update order
export const updateOrder = (data: UpdateResourceOrderRequest):
Promise<UpdateResourceOrderResponse> => {
  return request('/r/Adaptor/BenefitRpcI/updateOrder', [data]);
};

// Get order detail
export const getOrderDetail = (data: GetResourceOrderRequest) => {
  return request('/r/Adaptor/BenefitRpcI/getOrderDetail', [data]);
};

// Get order list
export const getOrderList = (data: PageResourceOrderRequest) => {
  return request('/r/Adaptor/BenefitRpcI/pageOrders', [data]);
};

// Cancel order
export const cancelOrder = (data: CancelBenefitOrderRequest): Promise<CancelBenefitOrderResponse> => {
  return request('/r/Adaptor/BenefitRpcI/cancelOrder', [data]);
};

// Check rejected order
export const checkRejectedOrder = (data: CheckRejectedOrderRequest): Promise<CheckRejectedOrderResponse> => {
  return request('/r/Adaptor/BenefitRpcI/checkRejectedOrder', [data]);
};
