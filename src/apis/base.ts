import lwp$ from '@ali/dingtalk-jsapi/api/internal/request/lwp';
import { isDingTalk } from '@/utils/jsapi';
import { lwpWebClientManager } from '@/apis/lwpWebClientManager';

// 统一的请求函数，自动在钉钉 JS-API 和 LwpWebClient 之间切换
export default async function request<T = unknown>(
  uri: string,
  body: unknown[] = [],
  headers: Record<string, string> = {},
  timeout?: number,
): Promise<T> {
  // Check if running in DingTalk environment
  if (isDingTalk()) {
    // Use DingTalk JS-API for native DingTalk environment
    const response = await lwp$({
      uri,
      body,
      headers,
    });

    const responseOK = response.code === 200;

    if (responseOK) {
      return response.body as T;
    }

    throw new Error(response.body?.reason || `Request failed with code: ${response.code}`);
  } else {
    // Use LwpWebClient for browser environment
    return await lwpWebClientManager.sendRequest<T>(uri, body, headers, timeout);
  }
}
