import request from './base';

// Generate video request parameters interface
export interface GenerateVideoRequest {
  imageInfo: string; // JSON string containing image information (e.g., {"url": "image_url"})
  positivePrompt?: string; // Positive prompt for video generation
  negativePrompt?: string; // Negative prompt for video generation
  quality?: string; // Video quality setting
  duration?: number; // Video duration in seconds
  [key: string]: any; // Allow additional parameters
}

// Check video status request parameters interface
export interface CheckVideoStatusRequest {
  uuid: string; // Video generation task UUID
}

// List videos request parameters interface
export interface ListVideosRequest {
  limit: number; // Number of videos to fetch
  nextUuid?: string; // UUID for pagination
  [key: string]: any; // Allow additional parameters
}

// Generate GIF request parameters interface
export interface GenerateGifRequest {
  uuid: string; // Source video UUID
  [key: string]: any; // Allow additional parameters
}

// Check GIF status request parameters interface
export interface CheckGifStatusRequest {
  uuid: string; // GIF generation task UUID
}

// Regenerate video request parameters interface
export interface ReGenerateVideoRequest {
  fromUuid: string; // Original video UUID
  imageInfo?: string; // Updated image information
  positivePrompt?: string; // Updated positive prompt
  negativePrompt?: string; // Updated negative prompt
  [key: string]: any; // Allow additional parameters
}

// Rate video request parameters interface
export interface RateVideoRequest {
  uuid: string; // Video UUID to rate
  rating: 0 | 1 | -1; // Rating value
}

// Upload image request parameters interface
export interface UploadImageRequest {
  file: File | Blob; // Image file to upload
  [key: string]: any; // Allow additional parameters
}

// Remove video request parameters interface
export interface RemoveVideoRequest {
  uuid: string; // Video UUID to remove
}

// Generate video (生成视频)
export const generateVideo = (data: GenerateVideoRequest) => {
  return request('/r/Adaptor/MaterialRpcI/generateVideo', [data]);
};

// Check video generation status (查看视频生成状态)
export const checkVideoStatus = (data: CheckVideoStatusRequest) => {
  return request('/r/Adaptor/MaterialRpcI/checkVideoStatus', [data]);
};

// Generate GIF (生成GIF)
export const generateGif = (data: GenerateGifRequest) => {
  return request('/r/Adaptor/MaterialRpcI/generateGif', [data]);
};

// Check GIF generation status (轮询GIF的生成结果)
export const checkGifStatus = (data: CheckGifStatusRequest) => {
  return request('/r/Adaptor/MaterialRpcI/checkGifStatus', [data]);
};

// Regenerate video (重新生成视频)
export const reGenerateVideo = (data: ReGenerateVideoRequest) => {
  return request('/r/Adaptor/MaterialRpcI/reGenerateVideo', [data]);
};

// Get video list (获取视频列表)
export const listVideos = (data: ListVideosRequest) => {
  return request('/r/Adaptor/MaterialRpcI/listVideos', [data]);
};

// Rate video (评价视频)
export const rateVideo = (data: RateVideoRequest) => {
  return request('/r/Adaptor/MaterialRpcI/rateVideo', [data]);
};

// Upload image (上传图片)
export const uploadImage = (data: UploadImageRequest) => {
  return request('/r/Adaptor/FileRpcI/upload', [data]);
};

// Delete video (删除视频)
export const removeVideo = (data: RemoveVideoRequest) => {
  return request('/r/Adaptor/MaterialRpcI/removeVideo', [data]);
};
