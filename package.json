{"name": "j-agent-web", "version": "1.0.0", "description": "J项目Agent选品仓库", "repository": {"type": "git", "url": "**************************:dingding/J-dingtalk.git"}, "author": "<EMAIL>", "scripts": {"build": "webpack --config webpack/config/webpack.prod.config.js --mode=production --progress", "dev": "webpack serve --config webpack/config/webpack.dev.config.js --mode=development", "f2elint-fix": "f2elint fix", "f2elint-scan": "f2elint scan", "lint": "eslint src --fix --ext .ts,.tsx ", "start": "npm run dev && npm run lint && npm run stylelint", "stylelint": "stylelint src/**/*.less", "build:z": "webpack --analyze --config webpack/config/webpack.prod.config.js --mode=production --progress ", "update-i18n": "update-i18n ./translationKeys.js ./src/i18n -n j-agent-web -w \"zh_CN|en_US|ja_JP\"", "fetch-webim-sign": "ts-node scripts/fetch-webim-sign.js"}, "husky": {"hooks": {"pre-commit": "f2elint commit-file-scan", "commit-msg": "f2elint commit-msg-scan"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "f2elint exec eslint", "src/**/*.{less,css,acss}": "f2elint exec stylelint"}, "browserslist": ["iOS >= 7", "Android >= 4", "ChromeAndroid >= 40", "UCAndroid >= 11", "Chrome >= 40"], "dependencies": {"@ali/dd-upload": "1.4.3", "@ali/ding-icons": "0.1.90", "@ali/ding-mediaid": "^2.3.7", "@ali/dingtalk-i18n": "^6.8.4", "@ali/dingtalk-jsapi": "3.1.4", "@ali/lwp-web-client": "4.1.8", "@ant-design/icons": "^5.2.6", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "ahooks": "^3.9.0", "ali-oss": "^6.18.0", "antd": "^5.11.0", "axios": "^1.9.0", "blakejs": "^1.2.1", "dingtalk-design-desktop": "^2.4.8", "dingtalk-design-mobile": "^2.6.7", "dingtalk-theme": "^6.0.12", "eslint": "^8.0.0", "eslint-config-ali": "^14.0.0", "he": "^1.2.0", "js-sha3": "^0.9.3", "react": "18.3.1", "react-dom": "18.3.1", "react-lazy-load-image-component": "^1.6.2", "react-markdown": "^9.0.0", "react-photo-view": "^0.5.7", "react-router": "^6.26.2", "react-router-dom": "^6.26.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "resize-observer-polyfill": "^1.5.1", "tailwindcss": "^3.4.12", "uuid": "^9.0.1"}, "devDependencies": {"@ali/dingtalk-aone-hook": "0.0.10", "@ali/dingtalk-medusa": "^10.1.0", "@ali/dtest-toolbox": "^0.0.79", "@babel/core": "^7.13.16", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.13.15", "@babel/preset-react": "^7.13.13", "@babel/preset-typescript": "^7.13.0", "@types/node": "^24.3.0", "@types/react": "^17.0.4", "@types/react-css-modules": "^4.6.2", "@types/react-dom": "^17.0.3", "autoprefixer": "^10.2.5", "babel-loader": "^8.2.2", "babel-plugin-react-css-modules": "^5.2.6", "clean-webpack-plugin": "^4.0.0-alpha.0", "css-loader": "^5.2.4", "css-minimizer-webpack-plugin": "^2.0.0", "eslint-webpack-plugin": "^4.0.1", "f2elint": "^4.8.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.3.1", "husky": "^6.0.0", "less": "^4.1.1", "less-loader": "^8.1.1", "lint-staged": "^11.0.0", "mini-css-extract-plugin": "^1.5.0", "nacos": "^2.6.0", "portfinder": "^1.0.28", "portfinder-sync": "^0.0.2", "postcss": "^8.2.13", "postcss-less": "^4.0.1", "postcss-loader": "^5.2.0", "pre-push": "^0.1.1", "prettier": "^2.2.1", "style-loader": "^2.0.0", "ts-loader": "^9.1.1", "ts-node": "^10.9.2", "typescript": "^4.2.4", "url-loader": "^4.1.1", "webpack": "^5.35.1", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "^4.6.0", "webpack-dev-server": "^3.11.2", "yargs-parser": "^20.2.7"}, "engines": {"install-node": "18"}}