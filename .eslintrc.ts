module.exports = {
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  extends: [
    'ali/typescript/react',
    'plugin:@typescript-eslint/recommended'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  rules: {
    'react/jsx-uses-react': 'off',
    'react/react-in-jsx-scope': 'off',
    'react/jsx-no-bind': 0,
    'react/no-danger': 0,
    'react-hooks/exhaustive-deps': 0,
    'indent': 'off',
    'max-len': 'off',
    '@typescript-eslint/indent': [2, 2, { SwitchCase: 1 }],
  },
};
