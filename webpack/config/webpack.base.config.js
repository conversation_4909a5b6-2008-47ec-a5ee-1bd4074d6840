const MiniCssExtractPlugin = require('mini-css-extract-plugin'); // 从JS中分离css，成单独css文件
const ESLintPlugin = require('eslint-webpack-plugin'); // eslint
const rootPath = require('../utils/rootPath');
const pageList = require('../utils/pageList'); // 获取多页面入口
const getPublicPath = require('../utils/getPublicPath');
const entryMap = () => {
  const entry = {};
  pageList.forEach((page) => {
    entry[page] = rootPath(`src/pages/${page}/app.tsx`);
  });
  return entry;
}
module.exports = {
  entry: entryMap(),

  output: {
    filename: '[name]/index.js',
    publicPath: getPublicPath(),
    path: rootPath('dist'),
  },
  externals: {
    "lodash": 'var window._',
  },
  resolve: {
    alias: {
      '@': rootPath('src/'),
    },
    extensions: ['.ts', '.tsx', '.js', '.json'],
  },

  module: {
    rules: [
      // Special rule for lwp-client TypeScript files - use ts-loader
      {
        test: /\.ts$/,
        include: [rootPath('src/lwp-client')],
        use: [
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true, // Skip type checking for faster builds
            },
          },
        ],
      },
      // General TypeScript rule for other files - use babel-loader
      {
        test: /\.ts(x?)$/,
        exclude: [/node_modules/, rootPath('src/lwp-client')],
        include: [rootPath('src')],
        use: [
          'babel-loader',
        ],
      },
      {
        test: /\.css$/i,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader',
        ],
      },
      {
        test: /\.less$/i,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader',
          'less-loader',
        ],
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: 'img/[name].[hash:7].[ext]',
        },
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: 'media/[name].[hash:7].[ext]',
        },
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: 'fonts/[name].[hash:7].[ext]',
        },
      },
    ],
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: '[name]/index.css',
    }),
    new ESLintPlugin({
      context: rootPath('src'),
      emitError: false,
      emitWarning: true,
    }),
  ],
};
